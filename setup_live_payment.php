<?php
/**
 * Live Payment Setup Script
 * Run this after getting SSLCommerz Live Credentials
 */

$setup_complete = false;
$errors = [];
$success_messages = [];
$ssl_setup_mode = false;

// Check if SSL setup mode is requested
if (isset($_GET['sslsetup']) || (isset($_POST['action']) && $_POST['action'] == 'sslsetup')) {
    $ssl_setup_mode = true;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    // Handle SSL Setup
    if (isset($_POST['action']) && $_POST['action'] == 'sslsetup') {
        try {
            $domain = trim($_POST['domain']);
            $ssl_provider = $_POST['ssl_provider'];

            if (empty($domain)) {
                throw new Exception('ডোমেইন নাম দিন');
            }

            // Generate SSL setup instructions
            $ssl_instructions = generateSSLInstructions($domain, $ssl_provider);
            $success_messages[] = 'SSL সেটআপ গাইড তৈরি হয়েছে।';

            // Create SSL setup file
            $ssl_file_content = "# SSL Certificate Setup Guide for $domain\n\n";
            $ssl_file_content .= $ssl_instructions;

            if (file_put_contents('ssl_setup_guide.md', $ssl_file_content)) {
                $success_messages[] = 'SSL গাইড ফাইল (ssl_setup_guide.md) তৈরি হয়েছে।';
            }

        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
        return; // Don't process regular setup
    }
    try {
        // Get form data
        $store_id = trim($_POST['store_id']);
        $store_password = trim($_POST['store_password']);
        $domain = trim($_POST['domain']);
        $bank_account = trim($_POST['bank_account']);
        $bank_routing = trim($_POST['bank_routing']);
        $business_phone = trim($_POST['business_phone']);
        $business_email = trim($_POST['business_email']);
        
        // Validation
        if (empty($store_id) || empty($store_password)) {
            throw new Exception('Store ID এবং Store Password প্রয়োজন');
        }
        
        if (empty($domain) || !filter_var($domain, FILTER_VALIDATE_URL)) {
            throw new Exception('সঠিক ডোমেইন URL দিন (https:// সহ)');
        }
        
        if (!filter_var($business_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('সঠিক ইমেইল ঠিকানা দিন');
        }
        
        // Check if domain has SSL
        $ssl_check = @file_get_contents($domain);
        if ($ssl_check === false) {
            $errors[] = 'Warning: ডোমেইন অ্যাক্সেস করা যাচ্ছে না। SSL Certificate চেক করুন।';
        } else {
            $success_messages[] = 'ডোমেইন SSL Certificate সঠিক আছে।';
        }
        
        // Update payment configuration file
        $config_content = "<?php
/**
 * Live Payment Configuration - Auto Generated
 * Generated on: " . date('Y-m-d H:i:s') . "
 */

// Payment Environment Settings
define('PAYMENT_ENVIRONMENT', 'LIVE'); // Changed to LIVE
define('PAYMENT_CURRENCY', 'BDT');
define('APP_URL', '$domain');
define('PAYMENT_SUCCESS_URL', '$domain/payment_success.php');
define('PAYMENT_FAIL_URL', '$domain/payment_fail.php');
define('PAYMENT_CANCEL_URL', '$domain/payment_cancel.php');
define('PAYMENT_IPN_URL', '$domain/payment_ipn.php');

// SSLCommerz Live Credentials
define('SSLCZ_STORE_ID', '$store_id');
define('SSLCZ_STORE_PASSWORD', '$store_password');
define('SSLCZ_REQUEST_URL', 'https://securepay.sslcommerz.com/gwprocess/v4/api.php');
define('SSLCZ_VALIDATION_URL', 'https://securepay.sslcommerz.com/validator/api/validationserverAPI.php');

// Organization Bank Details
define('ORG_BANK_ACCOUNT', '$bank_account');
define('ORG_BANK_ROUTING', '$bank_routing');

// Business Information
define('BUSINESS_NAME', 'বন্ধুদের সংস্থা');
define('BUSINESS_PHONE', '$business_phone');
define('BUSINESS_EMAIL', '$business_email');
define('BUSINESS_WEBSITE', '$domain');

// Payment Limits
define('MIN_PAYMENT_AMOUNT', 10);
define('MAX_PAYMENT_AMOUNT', 500000);

// Security Settings
define('PAYMENT_TIMEOUT', 1800);
define('ENABLE_PAYMENT_LOGS', true);
define('ENABLE_EMAIL_NOTIFICATIONS', true);

// Auto-generated functions
function validatePaymentAmount(\$amount) {
    return (\$amount >= MIN_PAYMENT_AMOUNT && \$amount <= MAX_PAYMENT_AMOUNT);
}

function generateSecureTransactionId() {
    \$prefix = 'FO';
    \$timestamp = date('YmdHis');
    \$random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return \$prefix . \$timestamp . \$random;
}

function formatPaymentAmount(\$amount) {
    return '৳' . number_format(\$amount, 2);
}
?>";
        
        // Write configuration file
        if (file_put_contents('config/payment_config.php', $config_content)) {
            $success_messages[] = 'Payment configuration file আপডেট হয়েছে।';
        } else {
            throw new Exception('Configuration file লিখতে পারিনি। Permission চেক করুন।');
        }
        
        // Update database.php file
        $db_config = file_get_contents('config/database.php');
        if ($db_config) {
            // Update APP_URL
            $db_config = preg_replace("/define\('APP_URL', '[^']*'\);/", "define('APP_URL', '$domain');", $db_config);
            
            if (file_put_contents('config/database.php', $db_config)) {
                $success_messages[] = 'Database configuration আপডেট হয়েছে।';
            }
        }
        
        // Test SSLCommerz connection
        $test_data = [
            'store_id' => $store_id,
            'store_passwd' => $store_password,
            'total_amount' => 10,
            'currency' => 'BDT',
            'tran_id' => 'TEST_' . time(),
            'success_url' => $domain . '/payment_success.php',
            'fail_url' => $domain . '/payment_fail.php',
            'cancel_url' => $domain . '/payment_cancel.php',
            'cus_name' => 'Test Customer',
            'cus_email' => $business_email,
            'cus_phone' => $business_phone,
            'cus_add1' => 'Test Address',
            'cus_city' => 'Dhaka',
            'cus_country' => 'Bangladesh',
            'product_name' => 'Test Payment',
            'product_category' => 'Service',
            'product_profile' => 'general'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://securepay.sslcommerz.com/gwprocess/v4/api.php');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $test_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code == 200 && $response) {
            $result = json_decode($response, true);
            if ($result && isset($result['status']) && $result['status'] == 'SUCCESS') {
                $success_messages[] = 'SSLCommerz connection test সফল! Live payment ready.';
                $setup_complete = true;
            } else {
                $errors[] = 'SSLCommerz connection test ব্যর্থ। Credentials চেক করুন।';
            }
        } else {
            $errors[] = 'SSLCommerz server এ connection করতে পারিনি।';
        }
        
        // Create backup
        $backup_dir = 'backups';
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $backup_file = $backup_dir . '/config_backup_' . date('Y-m-d_H-i-s') . '.zip';
        // Simple backup (you can enhance this)
        copy('config/payment_config.php', $backup_dir . '/payment_config_backup.php');
        $success_messages[] = 'Configuration backup তৈরি হয়েছে।';
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Payment Setup - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="gradient-bg text-white py-8">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h1 class="text-3xl font-bold mb-2">
                <i class="fas fa-rocket mr-3"></i>
                Live Payment Setup
            </h1>
            <p class="text-blue-100">SSLCommerz Live Credentials দিয়ে রিয়েল পেমেন্ট চালু করুন</p>

            <!-- Navigation Menu -->
            <div class="mt-6 flex justify-center space-x-4">
                <a href="setup_live_payment.php" class="<?php echo !$ssl_setup_mode ? 'bg-white text-blue-600' : 'bg-blue-600 text-white'; ?> px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors">
                    <i class="fas fa-cog mr-2"></i>
                    Main Setup
                </a>
                <a href="setup_live_payment.php?sslsetup=1" class="<?php echo $ssl_setup_mode ? 'bg-white text-blue-600' : 'bg-blue-600 text-white'; ?> px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors">
                    <i class="fas fa-shield-alt mr-2"></i>
                    SSL Setup
                </a>
                <a href="LIVE_PAYMENT_SETUP.md" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    <i class="fas fa-book mr-2"></i>
                    Full Guide
                </a>
            </div>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 py-8">
        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    সমস্যা হয়েছে:
                </h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($success_messages)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">
                    <i class="fas fa-check-circle mr-2"></i>
                    সফল:
                </h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($success_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($ssl_setup_mode): ?>
            <!-- SSL Setup Mode -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">
                    <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                    SSL Certificate Setup
                </h2>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        কেন SSL Certificate প্রয়োজন?
                    </h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• রিয়েল পেমেন্টের জন্য HTTPS বাধ্যতামূলক</li>
                        <li>• SSLCommerz শুধুমাত্র HTTPS সাইটে কাজ করে</li>
                        <li>• গ্রাহকদের তথ্য নিরাপদ রাখে</li>
                        <li>• Google ranking এ সাহায্য করে</li>
                    </ul>
                </div>

                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="sslsetup">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">আপনার ডোমেইন নাম *</label>
                        <input type="text" name="domain" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
                               placeholder="friendsorg.com (https:// ছাড়া)" required>
                        <p class="text-xs text-gray-500 mt-1">শুধু ডোমেইন নাম দিন, https:// দেবেন না</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">SSL Provider নির্বাচন করুন *</label>
                        <div class="grid md:grid-cols-2 gap-4">
                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="ssl_provider" value="letsencrypt" class="mr-3" required>
                                <div>
                                    <div class="font-medium text-green-600">Let's Encrypt</div>
                                    <div class="text-sm text-gray-500">ফ্রি ও সবচেয়ে জনপ্রিয়</div>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="ssl_provider" value="cloudflare" class="mr-3">
                                <div>
                                    <div class="font-medium text-blue-600">Cloudflare</div>
                                    <div class="text-sm text-gray-500">ফ্রি ও সহজ সেটআপ</div>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="ssl_provider" value="cpanel" class="mr-3">
                                <div>
                                    <div class="font-medium text-purple-600">cPanel SSL</div>
                                    <div class="text-sm text-gray-500">হোস্টিং প্যানেলে</div>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="ssl_provider" value="manual" class="mr-3">
                                <div>
                                    <div class="font-medium text-gray-600">Manual Setup</div>
                                    <div class="text-sm text-gray-500">কাস্টম সেটআপ</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="flex space-x-4">
                        <button type="submit" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            SSL Setup Guide ডাউনলোড করুন
                        </button>
                        <a href="setup_live_payment.php" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg text-center">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Main Setup এ ফিরে যান
                        </a>
                    </div>
                </form>

                <?php if (!empty($success_messages)): ?>
                    <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 class="font-semibold text-green-800 mb-2">SSL Setup Guide তৈরি হয়েছে!</h4>
                        <div class="space-y-2">
                            <a href="ssl_setup_guide.md" download class="inline-flex items-center text-green-600 hover:text-green-700">
                                <i class="fas fa-download mr-2"></i>
                                ssl_setup_guide.md ডাউনলোড করুন
                            </a>
                            <p class="text-sm text-green-700">
                                এই গাইড ফলো করে SSL Certificate সেটআপ করুন, তারপর Live Payment Setup করুন।
                            </p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

        <?php elseif ($setup_complete): ?>
            <div class="bg-white rounded-xl shadow-lg p-8 text-center">
                <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
                <h2 class="text-2xl font-bold text-green-600 mb-4">Live Payment Setup সম্পন্ন! 🎉</h2>
                <p class="text-gray-600 mb-6">আপনার রিয়েল পেমেন্ট সিস্টেম এখন চালু আছে।</p>
                
                <div class="grid md:grid-cols-2 gap-4 mb-6">
                    <a href="payment.php" class="btn-primary py-3 px-6 rounded-lg inline-flex items-center justify-center">
                        <i class="fas fa-credit-card mr-2"></i>
                        পেমেন্ট টেস্ট করুন
                    </a>
                    <a href="dashboard.php" class="btn-secondary py-3 px-6 rounded-lg inline-flex items-center justify-center">
                        <i class="fas fa-chart-line mr-2"></i>
                        ড্যাশবোর্ড দেখুন
                    </a>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-2">গুরুত্বপূর্ণ:</h4>
                    <ul class="text-sm text-blue-700 text-left space-y-1">
                        <li>• প্রথমে ছোট amount (১০-১০০ টাকা) দিয়ে টেস্ট করুন</li>
                        <li>• সব payment methods টেস্ট করুন</li>
                        <li>• Configuration backup সংরক্ষণ করুন</li>
                        <li>• Payment logs নিয়মিত চেক করুন</li>
                    </ul>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">
                    <i class="fas fa-cog mr-2 text-blue-600"></i>
                    Live Payment Configuration
                </h2>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-yellow-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        প্রয়োজনীয় তথ্য:
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• SSLCommerz থেকে পাওয়া Live Store ID ও Password</li>
                        <li>• SSL Certificate সহ ডোমেইন (https://)</li>
                        <li>• ব্যাংক অ্যাকাউন্ট তথ্য</li>
                        <li>• ব্যবসার যোগাযোগের তথ্য</li>
                    </ul>

                    <div class="mt-4 pt-3 border-t border-yellow-300">
                        <p class="text-sm text-yellow-800 mb-2">
                            <i class="fas fa-shield-alt mr-1"></i>
                            SSL Certificate এখনো সেটআপ করেননি?
                        </p>
                        <a href="setup_live_payment.php?sslsetup=1" class="inline-flex items-center text-yellow-700 hover:text-yellow-900 font-medium">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            SSL Setup Guide পান
                        </a>
                    </div>
                </div>

                <form method="POST" class="space-y-6">
                    <!-- SSLCommerz Credentials -->
                    <div class="border-b pb-6">
                        <h3 class="text-lg font-semibold mb-4">SSLCommerz Live Credentials</h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Store ID *</label>
                                <input type="text" name="store_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="friendsorg_live_XXXXX" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Store Password *</label>
                                <input type="password" name="store_password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="Your secure password" required>
                            </div>
                        </div>
                    </div>

                    <!-- Domain Settings -->
                    <div class="border-b pb-6">
                        <h3 class="text-lg font-semibold mb-4">ডোমেইন সেটিংস</h3>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Website URL (HTTPS) *</label>
                            <input type="url" name="domain" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                   placeholder="https://yourdomain.com" required>
                            <p class="text-xs text-gray-500 mt-1">SSL Certificate সহ ডোমেইন দিন</p>
                        </div>
                    </div>

                    <!-- Bank Information -->
                    <div class="border-b pb-6">
                        <h3 class="text-lg font-semibold mb-4">ব্যাংক তথ্য</h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bank Account Number</label>
                                <input type="text" name="bank_account" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="****************">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Routing Number</label>
                                <input type="text" name="bank_routing" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="*********">
                            </div>
                        </div>
                    </div>

                    <!-- Business Information -->
                    <div class="border-b pb-6">
                        <h3 class="text-lg font-semibold mb-4">ব্যবসার তথ্য</h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Business Phone *</label>
                                <input type="tel" name="business_phone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="+*************" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Business Email *</label>
                                <input type="email" name="business_email" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="<EMAIL>" required>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg">
                            <i class="fas fa-rocket mr-2"></i>
                            Live Payment Setup করুন
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Help Section -->
        <div class="mt-8 bg-gray-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">
                <i class="fas fa-question-circle mr-2 text-blue-600"></i>
                সাহায্য প্রয়োজন?
            </h3>
            <div class="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-medium mb-2">SSLCommerz সাপোর্ট:</h4>
                    <p>Phone: +৮৮০-২-৫৫০৪৮৮৮৮</p>
                    <p>Email: <EMAIL></p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">ডকুমেন্টেশন:</h4>
                    <p><a href="LIVE_PAYMENT_SETUP.md" class="text-blue-600 hover:underline">Setup Guide পড়ুন</a></p>
                    <p><a href="https://developer.sslcommerz.com" class="text-blue-600 hover:underline" target="_blank">API Documentation</a></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php
/**
 * Generate SSL setup instructions based on provider
 */
function generateSSLInstructions($domain, $provider) {
    $instructions = "";

    switch ($provider) {
        case 'letsencrypt':
            $instructions = "
## Let's Encrypt SSL Setup (Free)

### Requirements:
- Domain pointing to your server
- SSH access to server
- Certbot installed

### Steps:

1. **Install Certbot:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install certbot python3-certbot-apache

# CentOS/RHEL
sudo yum install certbot python3-certbot-apache
```

2. **Get SSL Certificate:**
```bash
sudo certbot --apache -d $domain
```

3. **Auto-renewal setup:**
```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

4. **Verify SSL:**
- Visit: https://$domain
- Check for green lock icon
- Test at: https://www.ssllabs.com/ssltest/

### Troubleshooting:
- Make sure domain points to your server IP
- Check firewall allows port 80 and 443
- Verify Apache is running
";
            break;

        case 'cloudflare':
            $instructions = "
## Cloudflare SSL Setup (Free)

### Steps:

1. **Create Cloudflare Account:**
- Go to: https://cloudflare.com
- Sign up for free account

2. **Add Your Domain:**
- Click 'Add a Site'
- Enter: $domain
- Choose Free plan

3. **Update Nameservers:**
- Copy Cloudflare nameservers
- Update at your domain registrar
- Wait 24-48 hours for propagation

4. **Enable SSL:**
- Go to SSL/TLS tab
- Set to 'Flexible' or 'Full'
- Enable 'Always Use HTTPS'

5. **Configure Origin Server:**
```apache
# Add to .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

6. **Verify SSL:**
- Visit: https://$domain
- Check Cloudflare SSL status
";
            break;

        case 'cpanel':
            $instructions = "
## cPanel SSL Setup

### Steps:

1. **Access cPanel:**
- Login to your hosting cPanel
- Look for 'SSL/TLS' section

2. **Let's Encrypt (if available):**
- Find 'Let's Encrypt SSL'
- Select domain: $domain
- Click 'Issue'

3. **Manual SSL Upload:**
- If you have SSL certificate files
- Go to 'SSL/TLS' > 'Manage SSL Sites'
- Upload Certificate, Private Key, CA Bundle

4. **Force HTTPS:**
- Go to 'SSL/TLS' > 'Force HTTPS Redirect'
- Enable for $domain

5. **Verify:**
- Visit: https://$domain
- Check for secure connection
";
            break;

        case 'manual':
            $instructions = "
## Manual SSL Setup

### Option 1: Purchase SSL Certificate

1. **Buy SSL from:**
- Namecheap
- GoDaddy
- Comodo
- DigiCert

2. **Generate CSR:**
```bash
openssl req -new -newkey rsa:2048 -nodes -keyout $domain.key -out $domain.csr
```

3. **Submit CSR to SSL provider**
4. **Download certificate files**
5. **Install on server**

### Option 2: Self-Signed (Development Only)

```bash
# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout $domain.key -out $domain.crt -days 365 -nodes

# Apache configuration
<VirtualHost *:443>
    ServerName $domain
    DocumentRoot /path/to/your/site
    SSLEngine on
    SSLCertificateFile /path/to/$domain.crt
    SSLCertificateKeyFile /path/to/$domain.key
</VirtualHost>
```

### Nginx Configuration:
```nginx
server {
    listen 443 ssl;
    server_name $domain;

    ssl_certificate /path/to/$domain.crt;
    ssl_certificate_key /path/to/$domain.key;

    root /path/to/your/site;
    index index.php index.html;
}
```
";
            break;

        default:
            $instructions = "
## General SSL Setup Guide

### Choose Your Method:

1. **Let's Encrypt (Free & Recommended)**
2. **Cloudflare (Free & Easy)**
3. **cPanel SSL (If available)**
4. **Purchase SSL Certificate**

### Verification Steps:
1. Visit: https://$domain
2. Check for green lock icon
3. Test at: https://www.ssllabs.com/ssltest/
4. Verify all pages load over HTTPS

### Common Issues:
- Mixed content warnings
- Redirect loops
- Certificate not trusted
- Domain not pointing to server
";
    }

    return $instructions;
}
?>
