<?php
require_once 'config/database.php';
require_once 'includes/SSLCommerz.php';

$transaction_id = $_POST['tran_id'] ?? $_GET['tran_id'] ?? '';

// Update transaction status to cancelled
if ($transaction_id && $db) {
    try {
        $update_query = "UPDATE transactions SET status = 'cancelled' WHERE transaction_id = ?";
        $stmt = $db->prepare($update_query);
        $stmt->execute([$transaction_id]);
        
        // Log cancelled payment
        $sslcommerz = new SSLCommerz();
        $sslcommerz->logPayment($transaction_id, 'cancelled', 'Payment cancelled by user');
    } catch (Exception $e) {
        error_log("Failed to update transaction: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট বাতিল - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users text-2xl text-orange-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">বন্ধুদের সংস্থা</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.php" class="text-gray-600 hover:text-orange-600">হোম</a>
                    <a href="dashboard.php" class="text-gray-600 hover:text-orange-600">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="text-gray-600 hover:text-orange-600">পেমেন্ট</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 py-12">
        <div class="bg-white rounded-xl shadow-lg p-8 text-center">
            <i class="fas fa-ban text-8xl text-orange-500 mb-6"></i>
            
            <h1 class="text-4xl font-bold text-orange-600 mb-4">
                পেমেন্ট বাতিল করা হয়েছে
            </h1>
            
            <p class="text-xl text-gray-600 mb-8">
                আপনি পেমেন্ট প্রক্রিয়া বাতিল করেছেন। কোনো অর্থ কাটা হয়নি।
            </p>

            <?php if ($transaction_id): ?>
            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-8">
                <p class="text-orange-700">
                    <strong>লেনদেন নম্বর:</strong> <?php echo htmlspecialchars($transaction_id); ?>
                </p>
                <p class="text-orange-600 text-sm mt-2">
                    <strong>স্ট্যাটাস:</strong> বাতিল করা হয়েছে
                </p>
            </div>
            <?php endif; ?>

            <!-- Information -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold mb-4">কি ঘটেছে?</h3>
                <div class="text-left space-y-3 text-gray-700">
                    <p><i class="fas fa-info-circle text-blue-500 mr-2"></i>আপনি পেমেন্ট প্রক্রিয়া বাতিল করেছেন</p>
                    <p><i class="fas fa-shield-alt text-green-500 mr-2"></i>আপনার কোনো অর্থ কাটা হয়নি</p>
                    <p><i class="fas fa-clock text-orange-500 mr-2"></i>লেনদেনটি সিস্টেমে বাতিল হিসেবে চিহ্নিত</p>
                    <p><i class="fas fa-redo text-purple-500 mr-2"></i>আপনি যেকোনো সময় আবার চেষ্টা করতে পারেন</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-4">
                <a href="payment.php" class="btn-primary px-8 py-3 inline-flex items-center">
                    <i class="fas fa-credit-card mr-2"></i>
                    আবার পেমেন্ট করুন
                </a>
                
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center">
                    <a href="dashboard.php" class="btn-secondary px-6 py-2">
                        <i class="fas fa-chart-line mr-2"></i>
                        ড্যাশবোর্ড
                    </a>
                    <a href="index.php" class="btn-secondary px-6 py-2">
                        <i class="fas fa-home mr-2"></i>
                        হোম পেজ
                    </a>
                </div>
            </div>

            <!-- Tips -->
            <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">
                    <i class="fas fa-lightbulb mr-2"></i>
                    পরামর্শ
                </h3>
                <div class="text-sm text-blue-700 text-left space-y-1">
                    <p>• পেমেন্টের আগে ব্যালেন্স চেক করুন</p>
                    <p>• ভাল ইন্টারনেট সংযোগ নিশ্চিত করুন</p>
                    <p>• কার্ডের তথ্য সঠিকভাবে দিন</p>
                    <p>• পেমেন্ট প্রক্রিয়া সম্পন্ন না হওয়া পর্যন্ত অপেক্ষা করুন</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto redirect after 10 seconds
        setTimeout(function() {
            if (confirm('পেমেন্ট পেজে ফিরে যেতে চান?')) {
                window.location.href = 'payment.php';
            }
        }, 10000);
    </script>
</body>
</html>
