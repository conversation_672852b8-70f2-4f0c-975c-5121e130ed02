<?php
/**
 * Real Payment Configuration
 * SSLCommerz Live Settings for Friends Organization
 */

// Payment Environment Settings
define('PAYMENT_ENVIRONMENT', 'SANDBOX'); // SANDBOX or LIVE
define('PAYMENT_CURRENCY', 'BDT');
define('PAYMENT_SUCCESS_URL', APP_URL . '/payment_success.php');
define('PAYMENT_FAIL_URL', APP_URL . '/payment_fail.php');
define('PAYMENT_CANCEL_URL', APP_URL . '/payment_cancel.php');
define('PAYMENT_IPN_URL', APP_URL . '/payment_ipn.php');

// SSLCommerz Live Credentials (You need to get these from SSLCommerz)
if (PAYMENT_ENVIRONMENT === 'LIVE') {
    // LIVE CREDENTIALS - Replace with your actual credentials
    define('SSLCZ_STORE_ID', 'friendsorg_live'); // Your actual Store ID
    define('SSLCZ_STORE_PASSWORD', 'your_live_password'); // Your actual Store Password
    define('SSLCZ_REQUEST_URL', 'https://securepay.sslcommerz.com/gwprocess/v4/api.php');
    define('SSLCZ_VALIDATION_URL', 'https://securepay.sslcommerz.com/validator/api/validationserverAPI.php');
} else {
    // SANDBOX CREDENTIALS - For testing
    define('SSLCZ_STORE_ID', 'testbox');
    define('SSLCZ_STORE_PASSWORD', 'qwerty');
    define('SSLCZ_REQUEST_URL', 'https://sandbox.sslcommerz.com/gwprocess/v4/api.php');
    define('SSLCZ_VALIDATION_URL', 'https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php');
}

// Organization Bank Details (Where money will be deposited)
define('ORG_BANK_NAME', 'Dutch Bangla Bank Limited');
define('ORG_BANK_ACCOUNT', '****************'); // Your actual bank account
define('ORG_BANK_ROUTING', '*********'); // DBBL routing number
define('ORG_BANK_BRANCH', 'Dhanmondi Branch');

// Business Information
define('BUSINESS_NAME', 'বন্ধুদের সংস্থা');
define('BUSINESS_ADDRESS', 'Dhaka, Bangladesh');
define('BUSINESS_PHONE', '+*************');
define('BUSINESS_EMAIL', '<EMAIL>');
define('BUSINESS_WEBSITE', 'https://friendsorg.com');

// Payment Limits
define('MIN_PAYMENT_AMOUNT', 10);
define('MAX_PAYMENT_AMOUNT', 500000);
define('DAILY_PAYMENT_LIMIT', 100000);
define('MONTHLY_PAYMENT_LIMIT', 1000000);

// Fee Structure (SSLCommerz charges)
$payment_fees = [
    'mobile_banking' => [
        'bkash' => ['percentage' => 1.85, 'fixed' => 5],
        'nagad' => ['percentage' => 1.85, 'fixed' => 5],
        'rocket' => ['percentage' => 1.85, 'fixed' => 5],
        'upay' => ['percentage' => 1.85, 'fixed' => 5]
    ],
    'card_payment' => [
        'visa' => ['percentage' => 2.9, 'fixed' => 5],
        'mastercard' => ['percentage' => 2.9, 'fixed' => 5],
        'amex' => ['percentage' => 3.5, 'fixed' => 10]
    ],
    'net_banking' => [
        'dbbl' => ['percentage' => 1.8, 'fixed' => 5],
        'brac' => ['percentage' => 1.8, 'fixed' => 5],
        'city' => ['percentage' => 1.8, 'fixed' => 5],
        'eastern' => ['percentage' => 1.8, 'fixed' => 5]
    ]
];

// Settlement Information
define('SETTLEMENT_CYCLE', 'T+1'); // T+1 means next business day
define('SETTLEMENT_TIME', '11:00 AM'); // When money is transferred
define('WEEKEND_SETTLEMENT', false); // No settlement on weekends

// Security Settings
define('PAYMENT_TIMEOUT', 1800); // 30 minutes
define('MAX_RETRY_ATTEMPTS', 3);
define('ENABLE_PAYMENT_LOGS', true);
define('ENABLE_EMAIL_NOTIFICATIONS', true);
define('ENABLE_SMS_NOTIFICATIONS', false);

// Webhook Security
define('WEBHOOK_SECRET', 'your_webhook_secret_key_here');
define('ENABLE_WEBHOOK_VERIFICATION', true);

/**
 * Calculate payment fee
 */
function calculatePaymentFee($amount, $method) {
    global $payment_fees;
    
    $fee = 0;
    
    // Determine fee category
    if (in_array($method, ['bkash', 'nagad', 'rocket', 'upay'])) {
        $category = 'mobile_banking';
    } elseif (in_array($method, ['visa', 'mastercard', 'amex'])) {
        $category = 'card_payment';
    } else {
        $category = 'net_banking';
    }
    
    if (isset($payment_fees[$category][$method])) {
        $fee_config = $payment_fees[$category][$method];
        $fee = ($amount * $fee_config['percentage'] / 100) + $fee_config['fixed'];
    }
    
    return round($fee, 2);
}

/**
 * Get net amount after fee deduction
 */
function getNetAmount($amount, $method) {
    $fee = calculatePaymentFee($amount, $method);
    return $amount - $fee;
}

/**
 * Validate payment amount
 */
function validatePaymentAmount($amount) {
    return ($amount >= MIN_PAYMENT_AMOUNT && $amount <= MAX_PAYMENT_AMOUNT);
}

/**
 * Check if payment method is available
 */
function isPaymentMethodAvailable($method, $amount) {
    $available_methods = [
        'bkash' => ['min' => 10, 'max' => 25000],
        'nagad' => ['min' => 10, 'max' => 25000],
        'rocket' => ['min' => 10, 'max' => 25000],
        'upay' => ['min' => 10, 'max' => 25000],
        'visa' => ['min' => 100, 'max' => 500000],
        'mastercard' => ['min' => 100, 'max' => 500000],
        'amex' => ['min' => 100, 'max' => 500000],
        'dbbl' => ['min' => 100, 'max' => 500000],
        'brac' => ['min' => 100, 'max' => 500000],
        'city' => ['min' => 100, 'max' => 500000],
        'eastern' => ['min' => 100, 'max' => 500000]
    ];
    
    if (!isset($available_methods[$method])) {
        return false;
    }
    
    $limits = $available_methods[$method];
    return ($amount >= $limits['min'] && $amount <= $limits['max']);
}

/**
 * Generate secure transaction ID
 */
function generateSecureTransactionId() {
    $prefix = 'FO'; // Friends Organization
    $timestamp = date('YmdHis');
    $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    $checksum = substr(md5($timestamp . $random), 0, 4);
    
    return $prefix . $timestamp . $random . strtoupper($checksum);
}

/**
 * Log payment activity
 */
function logPaymentActivity($transaction_id, $action, $details = '', $status = 'info') {
    if (!ENABLE_PAYMENT_LOGS) return;
    
    global $db;
    
    try {
        $log_data = [
            'transaction_id' => $transaction_id,
            'action' => $action,
            'details' => $details,
            'status' => $status,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $query = "INSERT INTO payment_activity_logs (transaction_id, action, details, status, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);
        $stmt->execute([
            $log_data['transaction_id'],
            $log_data['action'],
            $log_data['details'],
            $log_data['status'],
            $log_data['ip_address'],
            $log_data['user_agent'],
            $log_data['timestamp']
        ]);
        
    } catch (Exception $e) {
        error_log("Payment logging failed: " . $e->getMessage());
    }
}

/**
 * Send payment notification
 */
function sendPaymentNotification($type, $transaction_data) {
    if (!ENABLE_EMAIL_NOTIFICATIONS) return;
    
    $to = $transaction_data['customer_email'];
    $subject = '';
    $message = '';
    
    switch ($type) {
        case 'initiated':
            $subject = 'Payment Initiated - ' . BUSINESS_NAME;
            $message = "Your payment of ৳{$transaction_data['amount']} has been initiated. Transaction ID: {$transaction_data['transaction_id']}";
            break;
            
        case 'success':
            $subject = 'Payment Successful - ' . BUSINESS_NAME;
            $message = "Your payment of ৳{$transaction_data['amount']} has been completed successfully. Transaction ID: {$transaction_data['transaction_id']}";
            break;
            
        case 'failed':
            $subject = 'Payment Failed - ' . BUSINESS_NAME;
            $message = "Your payment of ৳{$transaction_data['amount']} could not be processed. Transaction ID: {$transaction_data['transaction_id']}";
            break;
    }
    
    if ($subject && $message) {
        send_email($to, $subject, $message);
    }
}

/**
 * Verify webhook signature
 */
function verifyWebhookSignature($payload, $signature) {
    if (!ENABLE_WEBHOOK_VERIFICATION) return true;
    
    $expected_signature = hash_hmac('sha256', $payload, WEBHOOK_SECRET);
    return hash_equals($expected_signature, $signature);
}

/**
 * Get payment status message in Bengali
 */
function getPaymentStatusMessage($status) {
    $messages = [
        'pending' => 'অপেক্ষমাণ',
        'initiated' => 'শুরু হয়েছে',
        'processing' => 'প্রক্রিয়াধীন',
        'completed' => 'সম্পন্ন',
        'success' => 'সফল',
        'failed' => 'ব্যর্থ',
        'cancelled' => 'বাতিল',
        'refunded' => 'ফেরত দেওয়া হয়েছে'
    ];
    
    return $messages[$status] ?? $status;
}

/**
 * Format amount for display
 */
function formatPaymentAmount($amount) {
    return '৳' . number_format($amount, 2);
}

/**
 * Get business hours
 */
function isBusinessHours() {
    $current_hour = (int)date('H');
    $current_day = date('N'); // 1 = Monday, 7 = Sunday
    
    // Business hours: 9 AM to 9 PM, Monday to Saturday
    return ($current_day >= 1 && $current_day <= 6) && ($current_hour >= 9 && $current_hour <= 21);
}

/**
 * Check if payment is allowed
 */
function isPaymentAllowed($amount, $member_id) {
    // Check business hours
    if (!isBusinessHours()) {
        return ['allowed' => false, 'reason' => 'Payment is only allowed during business hours (9 AM - 9 PM)'];
    }
    
    // Check amount limits
    if (!validatePaymentAmount($amount)) {
        return ['allowed' => false, 'reason' => 'Amount must be between ৳' . MIN_PAYMENT_AMOUNT . ' and ৳' . number_format(MAX_PAYMENT_AMOUNT)];
    }
    
    // Check daily limit (you can implement this based on your needs)
    // $daily_total = getDailyPaymentTotal($member_id);
    // if ($daily_total + $amount > DAILY_PAYMENT_LIMIT) {
    //     return ['allowed' => false, 'reason' => 'Daily payment limit exceeded'];
    // }
    
    return ['allowed' => true, 'reason' => ''];
}
?>
