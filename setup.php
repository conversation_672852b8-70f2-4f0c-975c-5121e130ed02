<?php
// Database Setup Script for Friends Organization

$host = 'localhost';
$username = 'root';
$password = '';
$database = 'friends_organization';

echo "<h2>বন্ধুদের সংস্থা - ডেটাবেস সেটআপ</h2>";

try {
    // First, connect without database to create it
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ ডেটাবেস '$database' তৈরি হয়েছে।</p>";
    
    // Now connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create tables
    $sql = "
    -- Members table
    CREATE TABLE IF NOT EXISTS members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20) NOT NULL,
        address TEXT NOT NULL,
        ssc_roll VARCHAR(50) NOT NULL,
        ssc_year VARCHAR(10) NOT NULL,
        ssc_board VARCHAR(50) NOT NULL,
        monthly_amount DECIMAL(10,2) NOT NULL CHECK (monthly_amount >= 100),
        photo_url VARCHAR(500),
        marksheet_url VARCHAR(500),
        password_hash VARCHAR(255),
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_status (status)
    );
    
    -- Transactions table
    CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
        type ENUM('deposit', 'profit', 'withdrawal') NOT NULL,
        description TEXT,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        payment_method VARCHAR(50),
        transaction_id VARCHAR(100),
        reference_number VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        INDEX idx_member_id (member_id),
        INDEX idx_status (status),
        INDEX idx_type (type)
    );
    
    -- Projects table
    CREATE TABLE IF NOT EXISTS projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        total_investment DECIMAL(15,2) NOT NULL CHECK (total_investment > 0),
        expected_return DECIMAL(5,2) CHECK (expected_return >= 0),
        actual_return DECIMAL(5,2) DEFAULT 0,
        start_date DATE NOT NULL,
        end_date DATE,
        status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status)
    );
    
    -- Admin users table
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        role ENUM('admin', 'super_admin') DEFAULT 'admin',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    
    -- Notifications table
    CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT,
        admin_id INT,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE
    );

    -- Payment logs table
    CREATE TABLE IF NOT EXISTS payment_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transaction_id VARCHAR(100) NOT NULL,
        status ENUM('initiated', 'completed', 'failed', 'cancelled') NOT NULL,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    );
    ";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ সকল টেবিল তৈরি হয়েছে।</p>";
    
    // Insert sample data
    $sample_data = "
    -- Insert sample member
    INSERT IGNORE INTO members (id, full_name, email, phone, address, ssc_roll, ssc_year, ssc_board, monthly_amount, status, created_at) VALUES
    (1, 'মোহাম্মদ রহিম', '<EMAIL>', '০১৭১২৩৪৫৬৭৮', 'ঢাকা, বাংলাদেশ', '123456', '2020', 'dhaka', 2000.00, 'approved', '2024-01-15 10:00:00'),
    (2, 'ফাতেমা খাতুন', '<EMAIL>', '০১৮১২৩৪৫৬৭৮', 'চট্টগ্রাম, বাংলাদেশ', '234567', '2019', 'chittagong', 1500.00, 'approved', '2024-02-01 11:00:00'),
    (3, 'আহমেদ হাসান', '<EMAIL>', '০১৯১২৩৪৫৬৭৮', 'সিলেট, বাংলাদেশ', '345678', '2021', 'sylhet', 3000.00, 'pending', '2024-12-01 12:00:00');
    
    -- Insert sample transactions
    INSERT IGNORE INTO transactions (id, member_id, amount, type, description, status, payment_method, created_at) VALUES
    (1, 1, 2000.00, 'deposit', 'মাসিক জমা - ডিসেম্বর ২০২৪', 'completed', 'bkash', '2024-12-01 10:00:00'),
    (2, 1, 500.00, 'profit', 'মাসিক লাভ বণ্টন', 'completed', NULL, '2024-11-25 15:00:00'),
    (3, 1, 2000.00, 'deposit', 'মাসিক জমা - নভেম্বর ২০২৪', 'completed', 'nagad', '2024-11-01 10:00:00'),
    (4, 1, 2000.00, 'deposit', 'মাসিক জমা - অক্টোবর ২০২৪', 'completed', 'bkash', '2024-10-01 10:00:00'),
    (5, 2, 1500.00, 'deposit', 'মাসিক জমা - ডিসেম্বর ২০২৪', 'completed', 'bkash', '2024-12-01 11:00:00');
    
    -- Insert sample projects
    INSERT IGNORE INTO projects (id, name, description, total_investment, expected_return, start_date, status) VALUES
    (1, 'ছোট ব্যবসা বিনিয়োগ প্রকল্প', 'স্থানীয় ছোট ব্যবসায়ীদের সাথে অংশীদারিত্বের মাধ্যমে বিনিয়োগ।', 500000.00, 15.00, '2024-01-01', 'active'),
    (2, 'কৃষি ও খামার প্রকল্প', 'আধুনিক কৃষি পদ্ধতি, মাছ চাষ এবং পোল্ট্রি ফার্মে বিনিয়োগ।', 300000.00, 12.50, '2024-02-01', 'active'),
    (3, 'প্রযুক্তি ও ডিজিটাল সেবা', 'ওয়েব ডেভেলপমেন্ট, মোবাইল অ্যাপ তৈরি এবং ডিজিটাল মার্কেটিং।', 800000.00, 20.00, '2024-03-01', 'active');
    
    -- Insert admin user
    INSERT IGNORE INTO admin_users (id, username, email, password_hash, full_name, role) VALUES
    (1, 'admin', '<EMAIL>', '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'super_admin');
    ";
    
    $pdo->exec($sample_data);
    echo "<p style='color: green;'>✓ নমুনা ডেটা যোগ করা হয়েছে।</p>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>সেটআপ সম্পন্ন! ✅</h3>";
    echo "<p>আপনার ডেটাবেস সফলভাবে তৈরি হয়েছে। এখন আপনি ওয়েবসাইট ব্যবহার করতে পারেন।</p>";
    echo "<p><strong>পরবর্তী ধাপ:</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.php' style='color: #007bff;'>হোম পেজে যান</a></li>";
    echo "<li><a href='dashboard.php' style='color: #007bff;'>ড্যাশবোর্ড দেখুন</a></li>";
    echo "<li><a href='register.php' style='color: #007bff;'>নতুন সদস্য নিবন্ধন</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>ডেমো লগইন তথ্য:</h4>";
    echo "<p><strong>সদস্য:</strong> <EMAIL></p>";
    echo "<p><strong>অ্যাডমিন:</strong> <EMAIL></p>";
    echo "<p><em>পাসওয়ার্ড: password (ডেমো)</em></p>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>সমস্যা সমাধানের উপায়:</h4>";
    echo "<ol>";
    echo "<li>XAMPP Control Panel এ MySQL চালু আছে কিনা চেক করুন</li>";
    echo "<li>phpMyAdmin এ যেতে পারেন কিনা টেস্ট করুন: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "<li>config/database.php ফাইলে ডেটাবেস তথ্য সঠিক আছে কিনা দেখুন</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডেটাবেস সেটআপ - বন্ধুদের সংস্থা</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- PHP output will be displayed here -->
    </div>
</body>
</html>
