import Link from 'next/link'
import { Users, TrendingUp, Shield, CreditCard } from 'lucide-react'

export default function Home() {
  return (
    <div className="max-w-7xl mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          বন্ধুদের সংস্থায় স্বাগতম
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          আমাদের সাথে যুক্ত হয়ে মাসিক সঞ্চয়ের মাধ্যমে লাভজনক বিনিয়োগে অংশগ্রহণ করুন। 
          একসাথে আমরা আরও শক্তিশালী।
        </p>
        <div className="space-x-4">
          <Link href="/register" className="btn-primary text-lg px-8 py-3">
            এখনই যুক্ত হন
          </Link>
          <Link href="/dashboard" className="btn-secondary text-lg px-8 py-3">
            ড্যাশবোর্ড দেখুন
          </Link>
        </div>
      </div>

      {/* Features Section */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <div className="card text-center">
          <Users className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">সহজ নিবন্ধন</h3>
          <p className="text-gray-600">SSC মার্কশিট ও ছবি দিয়ে সহজেই নিবন্ধন করুন</p>
        </div>
        
        <div className="card text-center">
          <CreditCard className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">অনলাইন পেমেন্ট</h3>
          <p className="text-gray-600">নিরাপদ অনলাইন পেমেন্ট সিস্টেম</p>
        </div>
        
        <div className="card text-center">
          <Shield className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">নিরাপদ ব্যাংকিং</h3>
          <p className="text-gray-600">সরাসরি ব্যাংকে জমা হওয়া অর্থ</p>
        </div>
        
        <div className="card text-center">
          <TrendingUp className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">লাভজনক বিনিয়োগ</h3>
          <p className="text-gray-600">পেশাদার বিনিয়োগ ব্যবস্থাপনা</p>
        </div>
      </div>

      {/* How it Works */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-center mb-8">কিভাবে কাজ করে</h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-primary-600">১</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">নিবন্ধন করুন</h3>
            <p className="text-gray-600">আপনার SSC মার্কশিট ও ছবি দিয়ে নিবন্ধন সম্পন্ন করুন</p>
          </div>
          
          <div className="text-center">
            <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-primary-600">২</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">মাসিক জমা</h3>
            <p className="text-gray-600">প্রতি মাসে নির্ধারিত পরিমাণ অর্থ অনলাইনে জমা দিন</p>
          </div>
          
          <div className="text-center">
            <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-primary-600">৩</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">লাভ পান</h3>
            <p className="text-gray-600">লাভজনক প্রকল্পে বিনিয়োগের মাধ্যমে মুনাফা অর্জন করুন</p>
          </div>
        </div>
      </div>
    </div>
  )
}
