<?php
require_once 'config/database.php';
require_once 'includes/SSLCommerz.php';

// Check if database connection exists
if (!$db) {
    header('Location: setup.php');
    exit;
}

$transaction_details = null;
$error_message = '';

// Handle SSLCommerz response
if ($_POST) {
    try {
        $val_id = $_POST['val_id'] ?? '';
        $tran_id = $_POST['tran_id'] ?? '';
        $amount = $_POST['amount'] ?? 0;
        $card_type = $_POST['card_type'] ?? '';
        $store_amount = $_POST['store_amount'] ?? 0;
        $bank_tran_id = $_POST['bank_tran_id'] ?? '';
        $status = $_POST['status'] ?? '';
        
        if ($status == 'VALID') {
            // Initialize SSLCommerz for validation
            $sslcommerz = new SSLCommerz();
            
            // Validate payment with SSLCommerz
            $validation_response = $sslcommerz->validatePayment(
                $val_id, 
                SSLCOMMERZ_STORE_ID, 
                SSLCOMMERZ_STORE_PASSWORD, 
                $_SERVER['REQUEST_URI']
            );
            
            if ($validation_response && $validation_response['status'] == 'VALID') {
                // Update transaction status in database
                $update_query = "UPDATE transactions SET
                    status = 'completed',
                    reference_number = ?
                    WHERE transaction_id = ? AND status = 'pending'";

                $stmt = $db->prepare($update_query);
                $stmt->execute([$bank_tran_id, $tran_id]);
                
                if ($stmt->rowCount() > 0) {
                    // Get transaction details
                    $details_query = "SELECT t.*, m.full_name, m.email FROM transactions t 
                                    LEFT JOIN members m ON t.member_id = m.id 
                                    WHERE t.transaction_id = ?";
                    $stmt = $db->prepare($details_query);
                    $stmt->execute([$tran_id]);
                    $transaction_details = $stmt->fetch();
                    
                    // Log successful payment
                    $sslcommerz->logPayment($tran_id, 'completed', "Bank Transaction ID: $bank_tran_id");
                    
                    // Send notification email
                    if ($transaction_details) {
                        $sslcommerz->sendPaymentNotification(
                            $transaction_details['email'],
                            $tran_id,
                            $amount,
                            'success'
                        );
                    }
                } else {
                    throw new Exception('Transaction not found or already processed');
                }
            } else {
                throw new Exception('Payment validation failed');
            }
        } else {
            throw new Exception('Payment was not successful');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        
        // Update transaction status to failed
        if (isset($tran_id)) {
            $update_query = "UPDATE transactions SET status = 'failed' WHERE transaction_id = ?";
            $stmt = $db->prepare($update_query);
            $stmt->execute([$tran_id]);
        }
    }
} else {
    $error_message = 'Invalid payment response';
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট সফল - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #f39c12;
            animation: confetti-fall 3s linear infinite;
        }
        @keyframes confetti-fall {
            0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
            100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users text-2xl text-green-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">বন্ধুদের সংস্থা</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.php" class="text-gray-600 hover:text-green-600">হোম</a>
                    <a href="dashboard.php" class="text-gray-600 hover:text-green-600">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="text-gray-600 hover:text-green-600">পেমেন্ট</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 py-12">
        <?php if ($transaction_details && !$error_message): ?>
            <!-- Success Page -->
            <div class="bg-white rounded-xl shadow-lg p-8 text-center relative overflow-hidden">
                <!-- Confetti Animation -->
                <div class="confetti" style="left: 10%; animation-delay: 0s; background: #e74c3c;"></div>
                <div class="confetti" style="left: 20%; animation-delay: 0.5s; background: #f39c12;"></div>
                <div class="confetti" style="left: 30%; animation-delay: 1s; background: #2ecc71;"></div>
                <div class="confetti" style="left: 40%; animation-delay: 1.5s; background: #3498db;"></div>
                <div class="confetti" style="left: 50%; animation-delay: 2s; background: #9b59b6;"></div>
                <div class="confetti" style="left: 60%; animation-delay: 0.3s; background: #e67e22;"></div>
                <div class="confetti" style="left: 70%; animation-delay: 0.8s; background: #1abc9c;"></div>
                <div class="confetti" style="left: 80%; animation-delay: 1.3s; background: #34495e;"></div>
                <div class="confetti" style="left: 90%; animation-delay: 1.8s; background: #e91e63;"></div>

                <div class="success-animation mb-6">
                    <i class="fas fa-check-circle text-8xl text-green-500"></i>
                </div>
                
                <h1 class="text-4xl font-bold text-green-600 mb-4">
                    পেমেন্ট সফল হয়েছে! 🎉
                </h1>
                
                <p class="text-xl text-gray-600 mb-8">
                    আপনার পেমেন্ট সফলভাবে সম্পন্ন হয়েছে এবং সরাসরি ব্যাংকে জমা হয়েছে।
                </p>

                <!-- Transaction Details -->
                <div class="bg-gray-50 rounded-lg p-6 mb-8 text-left">
                    <h2 class="text-2xl font-semibold mb-6 text-center">লেনদেনের বিবরণ</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">লেনদেন নম্বর:</span>
                                <span class="font-mono font-bold text-blue-600"><?php echo htmlspecialchars($transaction_details['transaction_id']); ?></span>
                            </div>
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">পরিমাণ:</span>
                                <span class="font-bold text-green-600 text-xl">৳<?php echo number_format($transaction_details['amount'], 2); ?></span>
                            </div>
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">পেমেন্ট পদ্ধতি:</span>
                                <span class="font-medium"><?php echo strtoupper($transaction_details['payment_method']); ?></span>
                            </div>
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">ব্যাংক রেফারেন্স:</span>
                                <span class="font-mono text-sm"><?php echo htmlspecialchars($transaction_details['reference_number'] ?? 'N/A'); ?></span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">সদস্যের নাম:</span>
                                <span class="font-medium"><?php echo htmlspecialchars($transaction_details['full_name']); ?></span>
                            </div>
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">তারিখ ও সময়:</span>
                                <span class="font-medium">
                                    <?php echo date('d F Y, h:i A', strtotime($transaction_details['created_at'])); ?>
                                </span>
                            </div>
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">বিবরণ:</span>
                                <span class="font-medium"><?php echo htmlspecialchars($transaction_details['description']); ?></span>
                            </div>
                            <div class="flex justify-between border-b pb-2">
                                <span class="text-gray-600">স্ট্যাটাস:</span>
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                    <i class="fas fa-check mr-1"></i>সম্পন্ন
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    <button onclick="downloadReceipt()" class="w-full md:w-auto btn-secondary flex items-center justify-center px-8 py-3">
                        <i class="fas fa-download mr-2"></i>
                        রসিদ ডাউনলোড করুন
                    </button>
                    
                    <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center">
                        <a href="dashboard.php" class="btn-primary px-8 py-3">
                            <i class="fas fa-chart-line mr-2"></i>
                            ড্যাশবোর্ডে যান
                        </a>
                        <a href="payment.php" class="btn-secondary px-8 py-3">
                            <i class="fas fa-credit-card mr-2"></i>
                            আরেকটি পেমেন্ট
                        </a>
                    </div>
                    
                    <a href="index.php" class="inline-flex items-center text-blue-600 hover:text-blue-700 mt-4">
                        <i class="fas fa-home mr-1"></i>
                        হোম পেজে ফিরে যান
                    </a>
                </div>

                <!-- Important Notice -->
                <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        গুরুত্বপূর্ণ তথ্য
                    </h3>
                    <ul class="text-sm text-blue-700 text-left space-y-1">
                        <li>• এই রসিদটি সংরক্ষণ করুন ভবিষ্যতের জন্য</li>
                        <li>• আপনার অর্থ সরাসরি সংস্থার ব্যাংক অ্যাকাউন্টে জমা হয়েছে</li>
                        <li>• যেকোনো সমস্যার জন্য লেনদেন নম্বর উল্লেখ করুন</li>
                        <li>• ২৪ ঘন্টার মধ্যে নিশ্চিতকরণ ইমেইল পাবেন</li>
                    </ul>
                </div>
            </div>

        <?php else: ?>
            <!-- Error Page -->
            <div class="bg-white rounded-xl shadow-lg p-8 text-center">
                <i class="fas fa-exclamation-triangle text-8xl text-red-500 mb-6"></i>
                
                <h1 class="text-4xl font-bold text-red-600 mb-4">
                    পেমেন্ট সমস্যা
                </h1>
                
                <p class="text-xl text-gray-600 mb-8">
                    <?php echo htmlspecialchars($error_message ?: 'পেমেন্ট প্রসেসিংয়ে সমস্যা হয়েছে।'); ?>
                </p>

                <div class="space-y-4">
                    <a href="payment.php" class="btn-primary px-8 py-3">
                        <i class="fas fa-redo mr-2"></i>
                        আবার চেষ্টা করুন
                    </a>
                    
                    <div class="mt-4">
                        <a href="index.php" class="text-blue-600 hover:text-blue-700">
                            <i class="fas fa-home mr-1"></i>
                            হোম পেজে ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Support Info -->
                <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-semibold mb-2">সাহায্য প্রয়োজন?</h3>
                    <p class="text-sm text-gray-600">
                        <i class="fas fa-phone mr-1"></i> +৮৮০১৭xxxxxxxx
                        <br>
                        <i class="fas fa-envelope mr-1"></i> <EMAIL>
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function downloadReceipt() {
            <?php if ($transaction_details): ?>
            const receiptContent = `
পেমেন্ট রসিদ - বন্ধুদের সংস্থা
=====================================

লেনদেন নম্বর: <?php echo $transaction_details['transaction_id']; ?>
পরিমাণ: ৳<?php echo number_format($transaction_details['amount'], 2); ?>
তারিখ: <?php echo date('d F Y, h:i A', strtotime($transaction_details['created_at'])); ?>
সদস্য: <?php echo $transaction_details['full_name']; ?>
বিবরণ: <?php echo $transaction_details['description']; ?>
পেমেন্ট পদ্ধতি: <?php echo strtoupper($transaction_details['payment_method']); ?>
ব্যাংক রেফারেন্স: <?php echo $transaction_details['reference_number'] ?? 'N/A'; ?>
স্ট্যাটাস: সফল

ধন্যবাদ!
বন্ধুদের সংস্থা
=====================================
            `;

            const blob = new Blob([receiptContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'receipt-<?php echo $transaction_details['transaction_id']; ?>.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            <?php endif; ?>
        }

        // Auto redirect after 30 seconds
        setTimeout(function() {
            if (confirm('ড্যাশবোর্ডে যেতে চান?')) {
                window.location.href = 'dashboard.php';
            }
        }, 30000);
    </script>
</body>
</html>
