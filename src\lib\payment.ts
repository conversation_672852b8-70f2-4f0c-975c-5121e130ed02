// Payment integration utilities

export interface PaymentData {
  amount: number
  currency: string
  description: string
  customerName: string
  customerEmail: string
  customerPhone: string
  orderId: string
}

export interface PaymentResponse {
  success: boolean
  transactionId?: string
  paymentUrl?: string
  error?: string
}

// SSLCommerz Integration (for Bangladesh)
export class SSLCommerzPayment {
  private storeId: string
  private storePassword: string
  private isLive: boolean

  constructor() {
    this.storeId = process.env.SSLCOMMERZ_STORE_ID || ''
    this.storePassword = process.env.SSLCOMMERZ_STORE_PASSWORD || ''
    this.isLive = process.env.SSLCOMMERZ_IS_LIVE === 'true'
  }

  async initiatePayment(data: PaymentData): Promise<PaymentResponse> {
    try {
      const baseUrl = this.isLive 
        ? 'https://securepay.sslcommerz.com'
        : 'https://sandbox.sslcommerz.com'

      const formData = new FormData()
      formData.append('store_id', this.storeId)
      formData.append('store_passwd', this.storePassword)
      formData.append('total_amount', data.amount.toString())
      formData.append('currency', data.currency)
      formData.append('tran_id', data.orderId)
      formData.append('success_url', `${process.env.APP_URL}/api/payment/success`)
      formData.append('fail_url', `${process.env.APP_URL}/api/payment/fail`)
      formData.append('cancel_url', `${process.env.APP_URL}/api/payment/cancel`)
      formData.append('cus_name', data.customerName)
      formData.append('cus_email', data.customerEmail)
      formData.append('cus_phone', data.customerPhone)
      formData.append('cus_add1', 'Dhaka, Bangladesh')
      formData.append('cus_city', 'Dhaka')
      formData.append('cus_country', 'Bangladesh')
      formData.append('shipping_method', 'NO')
      formData.append('product_name', data.description)
      formData.append('product_category', 'Service')
      formData.append('product_profile', 'general')

      const response = await fetch(`${baseUrl}/gwprocess/v4/api.php`, {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (result.status === 'SUCCESS') {
        return {
          success: true,
          paymentUrl: result.GatewayPageURL,
          transactionId: data.orderId
        }
      } else {
        return {
          success: false,
          error: result.failedreason || 'Payment initiation failed'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: 'Network error occurred'
      }
    }
  }

  async validatePayment(transactionId: string): Promise<boolean> {
    try {
      const baseUrl = this.isLive 
        ? 'https://securepay.sslcommerz.com'
        : 'https://sandbox.sslcommerz.com'

      const response = await fetch(
        `${baseUrl}/validator/api/validationserverAPI.php?val_id=${transactionId}&store_id=${this.storeId}&store_passwd=${this.storePassword}&format=json`
      )

      const result = await response.json()
      return result.status === 'VALID'
    } catch (error) {
      return false
    }
  }
}

// Stripe Integration (for international payments)
export class StripePayment {
  private publishableKey: string
  private secretKey: string

  constructor() {
    this.publishableKey = process.env.STRIPE_PUBLISHABLE_KEY || ''
    this.secretKey = process.env.STRIPE_SECRET_KEY || ''
  }

  async createPaymentIntent(data: PaymentData): Promise<PaymentResponse> {
    try {
      const response = await fetch('https://api.stripe.com/v1/payment_intents', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.secretKey}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          amount: (data.amount * 100).toString(), // Stripe uses cents
          currency: data.currency.toLowerCase(),
          description: data.description,
          metadata: {
            orderId: data.orderId,
            customerName: data.customerName,
            customerEmail: data.customerEmail
          }
        })
      })

      const result = await response.json()

      if (response.ok) {
        return {
          success: true,
          transactionId: result.id
        }
      } else {
        return {
          success: false,
          error: result.error?.message || 'Payment creation failed'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: 'Network error occurred'
      }
    }
  }
}

// Mobile Banking Integration (bKash, Nagad, etc.)
export class MobileBankingPayment {
  async initiateBkashPayment(data: PaymentData): Promise<PaymentResponse> {
    // This would integrate with bKash API
    // For demo purposes, we'll simulate the process
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        transactionId: `BKS${Date.now()}`,
        paymentUrl: `bkash://pay?amount=${data.amount}&merchant=friendsorg`
      }
    } catch (error) {
      return {
        success: false,
        error: 'bKash payment failed'
      }
    }
  }

  async initiateNagadPayment(data: PaymentData): Promise<PaymentResponse> {
    // This would integrate with Nagad API
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        transactionId: `NGD${Date.now()}`,
        paymentUrl: `nagad://pay?amount=${data.amount}&merchant=friendsorg`
      }
    } catch (error) {
      return {
        success: false,
        error: 'Nagad payment failed'
      }
    }
  }

  async initiateRocketPayment(data: PaymentData): Promise<PaymentResponse> {
    // This would integrate with Rocket API
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return {
        success: true,
        transactionId: `RKT${Date.now()}`,
        paymentUrl: `rocket://pay?amount=${data.amount}&merchant=friendsorg`
      }
    } catch (error) {
      return {
        success: false,
        error: 'Rocket payment failed'
      }
    }
  }
}

// Main payment service
export class PaymentService {
  private sslcommerz: SSLCommerzPayment
  private stripe: StripePayment
  private mobileBanking: MobileBankingPayment

  constructor() {
    this.sslcommerz = new SSLCommerzPayment()
    this.stripe = new StripePayment()
    this.mobileBanking = new MobileBankingPayment()
  }

  async processPayment(
    method: 'card' | 'mobile' | 'bank',
    provider: string,
    data: PaymentData
  ): Promise<PaymentResponse> {
    switch (method) {
      case 'card':
        if (data.currency === 'BDT') {
          return this.sslcommerz.initiatePayment(data)
        } else {
          return this.stripe.createPaymentIntent(data)
        }
      
      case 'mobile':
        switch (provider) {
          case 'bkash':
            return this.mobileBanking.initiateBkashPayment(data)
          case 'nagad':
            return this.mobileBanking.initiateNagadPayment(data)
          case 'rocket':
            return this.mobileBanking.initiateRocketPayment(data)
          default:
            return { success: false, error: 'Unsupported mobile banking provider' }
        }
      
      case 'bank':
        // For bank transfers, we might use SSLCommerz or direct bank API
        return this.sslcommerz.initiatePayment(data)
      
      default:
        return { success: false, error: 'Unsupported payment method' }
    }
  }

  generateOrderId(): string {
    return `ORD${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`
  }
}

// Utility functions
export const formatCurrency = (amount: number, currency: string = 'BDT'): string => {
  if (currency === 'BDT') {
    return `৳${amount.toLocaleString()}`
  }
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

export const validatePaymentAmount = (amount: number, min: number = 100, max: number = 100000): boolean => {
  return amount >= min && amount <= max
}
