<?php
require_once 'config/database.php';
require_once 'includes/SSLCommerz.php';

$transaction_id = $_POST['tran_id'] ?? $_GET['tran_id'] ?? '';
$error_reason = $_POST['error'] ?? 'Payment failed';

// Update transaction status to failed
if ($transaction_id && $db) {
    try {
        $update_query = "UPDATE transactions SET status = 'failed' WHERE transaction_id = ?";
        $stmt = $db->prepare($update_query);
        $stmt->execute([$transaction_id]);
        
        // Log failed payment
        $sslcommerz = new SSLCommerz();
        $sslcommerz->logPayment($transaction_id, 'failed', $error_reason);
    } catch (Exception $e) {
        error_log("Failed to update transaction: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট ব্যর্থ - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users text-2xl text-red-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">বন্ধুদের সংস্থা</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.php" class="text-gray-600 hover:text-red-600">হোম</a>
                    <a href="dashboard.php" class="text-gray-600 hover:text-red-600">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="text-gray-600 hover:text-red-600">পেমেন্ট</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 py-12">
        <div class="bg-white rounded-xl shadow-lg p-8 text-center">
            <i class="fas fa-times-circle text-8xl text-red-500 mb-6"></i>
            
            <h1 class="text-4xl font-bold text-red-600 mb-4">
                পেমেন্ট ব্যর্থ হয়েছে
            </h1>
            
            <p class="text-xl text-gray-600 mb-8">
                দুঃখিত! আপনার পেমেন্ট সম্পন্ন হয়নি। অনুগ্রহ করে আবার চেষ্টা করুন।
            </p>

            <?php if ($transaction_id): ?>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
                <p class="text-red-700">
                    <strong>লেনদেন নম্বর:</strong> <?php echo htmlspecialchars($transaction_id); ?>
                </p>
                <p class="text-red-600 text-sm mt-2">
                    <strong>কারণ:</strong> <?php echo htmlspecialchars($error_reason); ?>
                </p>
            </div>
            <?php endif; ?>

            <!-- Common Reasons -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8 text-left">
                <h3 class="text-lg font-semibold mb-4 text-center">সাধারণ কারণসমূহ</h3>
                <ul class="space-y-2 text-gray-700">
                    <li><i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>অপর্যাপ্ত ব্যালেন্স</li>
                    <li><i class="fas fa-wifi text-red-500 mr-2"></i>ইন্টারনেট সংযোগ সমস্যা</li>
                    <li><i class="fas fa-credit-card text-blue-500 mr-2"></i>কার্ডের তথ্য ভুল</li>
                    <li><i class="fas fa-clock text-orange-500 mr-2"></i>সেশন টাইমআউট</li>
                    <li><i class="fas fa-ban text-red-500 mr-2"></i>ব্যাংক দ্বারা প্রত্যাখ্যাত</li>
                </ul>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-4">
                <a href="payment.php" class="btn-primary px-8 py-3 inline-flex items-center">
                    <i class="fas fa-redo mr-2"></i>
                    আবার চেষ্টা করুন
                </a>
                
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center">
                    <a href="dashboard.php" class="btn-secondary px-6 py-2">
                        <i class="fas fa-chart-line mr-2"></i>
                        ড্যাশবোর্ড
                    </a>
                    <a href="index.php" class="btn-secondary px-6 py-2">
                        <i class="fas fa-home mr-2"></i>
                        হোম পেজ
                    </a>
                </div>
            </div>

            <!-- Support Info -->
            <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">
                    <i class="fas fa-headset mr-2"></i>
                    সাহায্য প্রয়োজন?
                </h3>
                <p class="text-sm text-blue-700">
                    <i class="fas fa-phone mr-1"></i> +৮৮০১৭xxxxxxxx
                    <br>
                    <i class="fas fa-envelope mr-1"></i> <EMAIL>
                    <br>
                    <i class="fas fa-clock mr-1"></i> সেবা সময়: সকাল ৯টা - রাত ৯টা
                </p>
            </div>
        </div>
    </div>

    <script>
        // Auto redirect after 15 seconds
        setTimeout(function() {
            if (confirm('পেমেন্ট পেজে ফিরে যেতে চান?')) {
                window.location.href = 'payment.php';
            }
        }, 15000);
    </script>
</body>
</html>
