'use client'

import { useState, useEffect } from 'react'
import { User, CreditCard, TrendingUp, Calendar, DollarSign, Users } from 'lucide-react'

interface UserData {
  name: string
  email: string
  phone: string
  memberSince: string
  monthlyAmount: number
  totalDeposited: number
  currentBalance: number
  totalProfit: number
}

interface Transaction {
  id: string
  date: string
  type: 'deposit' | 'profit'
  amount: number
  description: string
  status: 'completed' | 'pending'
}

export default function Dashboard() {
  const [userData, setUserData] = useState<UserData | null>(null)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading user data
    setTimeout(() => {
      setUserData({
        name: 'মোহাম্মদ রহিম',
        email: '<EMAIL>',
        phone: '০১৭১২৩৪৫৬৭৮',
        memberSince: '২০২৪-০১-১৫',
        monthlyAmount: 2000,
        totalDeposited: 24000,
        currentBalance: 26500,
        totalProfit: 2500
      })

      setTransactions([
        {
          id: '1',
          date: '২০২৪-১২-০১',
          type: 'deposit',
          amount: 2000,
          description: 'মাসিক জমা - ডিসেম্বর ২০২৪',
          status: 'completed'
        },
        {
          id: '2',
          date: '২০২ৄ-১১-২৫',
          type: 'profit',
          amount: 500,
          description: 'মাসিক লাভ বণ্টন',
          status: 'completed'
        },
        {
          id: '3',
          date: '২০২৪-১১-০১',
          type: 'deposit',
          amount: 2000,
          description: 'মাসিক জমা - নভেম্বর ২০২৪',
          status: 'completed'
        },
        {
          id: '4',
          date: '২০২৪-১০-০১',
          type: 'deposit',
          amount: 2000,
          description: 'মাসিক জমা - অক্টোবর ২০২৪',
          status: 'completed'
        }
      ])

      setLoading(false)
    }, 1000)
  }, [])

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    )
  }

  if (!userData) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">ড্যাশবোর্ড</h1>
          <p className="text-gray-600">অনুগ্রহ করে লগইন করুন</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">ড্যাশবোর্ড</h1>
        <p className="text-gray-600">স্বাগতম, {userData.name}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">মোট জমা</p>
              <p className="text-2xl font-bold text-gray-900">৳{userData.totalDeposited.toLocaleString()}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">বর্তমান ব্যালেন্স</p>
              <p className="text-2xl font-bold text-gray-900">৳{userData.currentBalance.toLocaleString()}</p>
            </div>
            <CreditCard className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">মোট লাভ</p>
              <p className="text-2xl font-bold text-green-600">৳{userData.totalProfit.toLocaleString()}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">মাসিক জমা</p>
              <p className="text-2xl font-bold text-gray-900">৳{userData.monthlyAmount.toLocaleString()}</p>
            </div>
            <Calendar className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Profile Information */}
        <div className="lg:col-span-1">
          <div className="card">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <User className="w-5 h-5 mr-2" />
              ব্যক্তিগত তথ্য
            </h2>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-600">নাম</p>
                <p className="font-medium">{userData.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">ইমেইল</p>
                <p className="font-medium">{userData.email}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">মোবাইল</p>
                <p className="font-medium">{userData.phone}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">সদস্য হওয়ার তারিখ</p>
                <p className="font-medium">{userData.memberSince}</p>
              </div>
            </div>
            <button className="w-full btn-secondary mt-4">
              প্রোফাইল আপডেট করুন
            </button>
          </div>

          {/* Quick Actions */}
          <div className="card mt-6">
            <h2 className="text-xl font-semibold mb-4">দ্রুত কাজ</h2>
            <div className="space-y-3">
              <button className="w-full btn-primary">
                মাসিক পেমেন্ট করুন
              </button>
              <button className="w-full btn-secondary">
                লেনদেনের ইতিহাস ডাউনলোড
              </button>
              <button className="w-full btn-secondary">
                সাহায্য ও সহায়তা
              </button>
            </div>
          </div>
        </div>

        {/* Transaction History */}
        <div className="lg:col-span-2">
          <div className="card">
            <h2 className="text-xl font-semibold mb-4">লেনদেনের ইতিহাস</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">তারিখ</th>
                    <th className="text-left py-2">বিবরণ</th>
                    <th className="text-left py-2">ধরন</th>
                    <th className="text-right py-2">পরিমাণ</th>
                    <th className="text-center py-2">অবস্থা</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((transaction) => (
                    <tr key={transaction.id} className="border-b hover:bg-gray-50">
                      <td className="py-3">{transaction.date}</td>
                      <td className="py-3">{transaction.description}</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          transaction.type === 'deposit' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {transaction.type === 'deposit' ? 'জমা' : 'লাভ'}
                        </span>
                      </td>
                      <td className="py-3 text-right font-medium">
                        <span className={transaction.type === 'profit' ? 'text-green-600' : 'text-gray-900'}>
                          ৳{transaction.amount.toLocaleString()}
                        </span>
                      </td>
                      <td className="py-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          transaction.status === 'completed' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transaction.status === 'completed' ? 'সম্পন্ন' : 'অপেক্ষমাণ'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Investment Overview */}
          <div className="card mt-6">
            <h2 className="text-xl font-semibold mb-4">বিনিয়োগের সংক্ষিপ্ত বিবরণ</h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900">চলমান প্রকল্প</h3>
                <p className="text-2xl font-bold text-blue-600">৩টি</p>
                <p className="text-sm text-blue-700">মোট বিনিয়োগ: ৳৫,০০,০০০</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-900">গড় মাসিক রিটার্ন</h3>
                <p className="text-2xl font-bold text-green-600">১২.৫%</p>
                <p className="text-sm text-green-700">গত ৬ মাসের গড়</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
