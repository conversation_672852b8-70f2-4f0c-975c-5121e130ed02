import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'your-supabase-url'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Member {
  id: string
  full_name: string
  email: string
  phone: string
  address: string
  ssc_roll: string
  ssc_year: string
  ssc_board: string
  monthly_amount: number
  photo_url?: string
  marksheet_url?: string
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  member_id: string
  amount: number
  type: 'deposit' | 'profit' | 'withdrawal'
  description: string
  status: 'pending' | 'completed' | 'failed'
  payment_method?: string
  transaction_id?: string
  created_at: string
}

export interface Project {
  id: string
  name: string
  description: string
  total_investment: number
  expected_return: number
  start_date: string
  end_date?: string
  status: 'active' | 'completed' | 'cancelled'
  created_at: string
}

// Helper functions for database operations
export const memberService = {
  // Create new member
  async createMember(memberData: Omit<Member, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('members')
      .insert([memberData])
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get member by ID
  async getMember(id: string) {
    const { data, error } = await supabase
      .from('members')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // Get member by email
  async getMemberByEmail(email: string) {
    const { data, error } = await supabase
      .from('members')
      .select('*')
      .eq('email', email)
      .single()
    
    if (error) throw error
    return data
  },

  // Update member
  async updateMember(id: string, updates: Partial<Member>) {
    const { data, error } = await supabase
      .from('members')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get all members
  async getAllMembers() {
    const { data, error } = await supabase
      .from('members')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }
}

export const transactionService = {
  // Create new transaction
  async createTransaction(transactionData: Omit<Transaction, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('transactions')
      .insert([transactionData])
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get transactions for a member
  async getMemberTransactions(memberId: string) {
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('member_id', memberId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Get all transactions
  async getAllTransactions() {
    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        members (
          full_name,
          email
        )
      `)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Update transaction status
  async updateTransactionStatus(id: string, status: Transaction['status']) {
    const { data, error } = await supabase
      .from('transactions')
      .update({ status })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

export const projectService = {
  // Create new project
  async createProject(projectData: Omit<Project, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('projects')
      .insert([projectData])
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Get all projects
  async getAllProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Get active projects
  async getActiveProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Update project
  async updateProject(id: string, updates: Partial<Project>) {
    const { data, error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}

// File upload helper
export const uploadFile = async (file: File, bucket: string, path: string) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file)
  
  if (error) throw error
  
  const { data: urlData } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)
  
  return urlData.publicUrl
}
