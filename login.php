<?php
session_start();
require_once 'config/database.php';

$error_message = '';

// Redirect if already logged in
if (isset($_SESSION['member_id'])) {
    header('Location: dashboard.php');
    exit();
}

// Handle login
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error_message = 'ইমেইল এবং পাসওয়ার্ড দিন';
    } else {
        try {
            $query = "SELECT * FROM members WHERE email = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$email]);
            $member = $stmt->fetch();
            
            if ($member && password_verify($password, $member['password'])) {
                $_SESSION['member_id'] = $member['id'];
                $_SESSION['member_name'] = $member['name'];
                $_SESSION['member_email'] = $member['email'];
                $_SESSION['member_role'] = $member['role'];
                
                header('Location: dashboard.php');
                exit();
            } else {
                $error_message = 'ভুল ইমেইল বা পাসওয়ার্ড';
            }
        } catch (Exception $e) {
            $error_message = 'লগইন করতে সমস্যা হয়েছে। আবার চেষ্টা করুন।';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-20 w-20 bg-white rounded-full flex items-center justify-center shadow-lg">
                <i class="fas fa-users text-3xl text-blue-600"></i>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-white">
                বন্ধুদের সংস্থা
            </h2>
            <p class="mt-2 text-sm text-blue-100">
                আপনার অ্যাকাউন্টে লগইন করুন
            </p>
        </div>

        <!-- Login Form -->
        <div class="login-card rounded-xl shadow-2xl p-8">
            <?php if ($error_message): ?>
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span><?php echo htmlspecialchars($error_message); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" class="space-y-6">
                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-gray-500"></i>
                        ইমেইল ঠিকানা
                    </label>
                    <input type="email" name="email" id="email" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                           placeholder="আপনার ইমেইল দিন"
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-gray-500"></i>
                        পাসওয়ার্ড
                    </label>
                    <div class="relative">
                        <input type="password" name="password" id="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-12"
                               placeholder="আপনার পাসওয়ার্ড দিন">
                        <button type="button" onclick="togglePassword()" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="password-toggle"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" name="remember" id="remember" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember" class="ml-2 text-sm text-gray-700">
                            আমাকে মনে রাখুন
                        </label>
                    </div>
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-500">
                        পাসওয়ার্ড ভুলে গেছেন?
                    </a>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    লগইন করুন
                </button>
            </form>

            <!-- Demo Credentials -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 mb-2">
                    <i class="fas fa-info-circle mr-2"></i>
                    ডেমো অ্যাকাউন্ট
                </h4>
                <div class="text-xs text-blue-700 space-y-1">
                    <div class="flex justify-between">
                        <span>Admin:</span>
                        <span><EMAIL> / admin123</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Member:</span>
                        <span><EMAIL> / member123</span>
                    </div>
                </div>
                <div class="mt-2 flex space-x-2">
                    <button onclick="fillDemo('admin')" 
                            class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                        Admin Login
                    </button>
                    <button onclick="fillDemo('member')" 
                            class="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700">
                        Member Login
                    </button>
                </div>
            </div>

            <!-- Register Link -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    নতুন সদস্য?
                    <a href="register.php" class="text-blue-600 hover:text-blue-500 font-medium">
                        রেজিস্ট্রেশন করুন
                    </a>
                </p>
            </div>

            <!-- Quick Links -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex justify-center space-x-4">
                    <a href="index.php" class="text-sm text-gray-600 hover:text-blue-600 flex items-center">
                        <i class="fas fa-home mr-1"></i>
                        হোম
                    </a>
                    <a href="payment.php" class="text-sm text-gray-600 hover:text-blue-600 flex items-center">
                        <i class="fas fa-credit-card mr-1"></i>
                        পেমেন্ট
                    </a>
                    <a href="setup.php" class="text-sm text-gray-600 hover:text-blue-600 flex items-center">
                        <i class="fas fa-cog mr-1"></i>
                        সেটআপ
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center">
            <p class="text-xs text-blue-100">
                © ২০২৪ বন্ধুদের সংস্থা। সকল অধিকার সংরক্ষিত।
            </p>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Fill demo credentials
        function fillDemo(type) {
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            
            if (type === 'admin') {
                emailField.value = '<EMAIL>';
                passwordField.value = 'admin123';
            } else if (type === 'member') {
                emailField.value = '<EMAIL>';
                passwordField.value = 'member123';
            }
        }

        // Auto-focus on email field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html>
