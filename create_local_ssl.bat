@echo off
echo Creating Local SSL Certificate for Development...
echo.

REM Create certificates directory
if not exist "certificates" mkdir certificates
cd certificates

REM Generate private key
openssl genrsa -out localhost.key 2048

REM Create certificate signing request
openssl req -new -key localhost.key -out localhost.csr -config ../ssl_config.conf

REM Generate self-signed certificate
openssl x509 -req -days 365 -in localhost.csr -signkey localhost.key -out localhost.crt -extensions v3_req -extfile ../ssl_config.conf

echo.
echo SSL Certificate created successfully!
echo Files created:
echo - certificates/localhost.key (Private Key)
echo - certificates/localhost.crt (Certificate)
echo.
echo Next steps:
echo 1. Configure Apache/Nginx to use these certificates
echo 2. Add localhost.crt to Windows Trusted Root Certificates
echo 3. Restart web server
echo.
pause
