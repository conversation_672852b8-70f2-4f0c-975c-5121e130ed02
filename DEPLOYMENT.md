# ডিপ্লয়মেন্ট গাইড - বন্ধুদের সংস্থা

এই গাইডে বিভিন্ন প্ল্যাটফর্মে আপনার অ্যাপ্লিকেশন ডিপ্লয় করার পদ্ধতি বর্ণনা করা হয়েছে।

## 🚀 Vercel এ ডিপ্লয়মেন্ট (সুপারিশকৃত)

Vercel হল Next.js অ্যাপ্লিকেশনের জন্য সবচেয়ে সহজ ডিপ্লয়মেন্ট অপশন।

### ধাপ ১: Vercel অ্যাকাউন্ট তৈরি করুন
1. [Vercel.com](https://vercel.com) এ যান
2. GitHub দিয়ে সাইন আপ করুন

### ধাপ ২: প্রকল্প ইমপোর্ট করুন
1. "New Project" ক্লিক করুন
2. আপনার GitHub রিপোজিটরি নির্বাচন করুন
3. "Import" ক্লিক করুন

### ধাপ ৩: Environment Variables সেট করুন
Vercel ড্যাশবোর্ডে নিচের variables যোগ করুন:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SSLCOMMERZ_STORE_ID=your-store-id
SSLCOMMERZ_STORE_PASSWORD=your-store-password
SSLCOMMERZ_IS_LIVE=false
APP_URL=https://your-domain.vercel.app
```

### ধাপ ৪: ডিপ্লয় করুন
1. "Deploy" বাটন ক্লিক করুন
2. কয়েক মিনিট অপেক্ষা করুন
3. আপনার সাইট লাইভ!

## 🐳 Docker দিয়ে ডিপ্লয়মেন্ট

### লোকাল Docker সেটআপ
```bash
# Docker image বিল্ড করুন
docker build -t friends-organization .

# Container চালু করুন
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_SUPABASE_URL=your-url \
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=your-key \
  friends-organization
```

### Docker Compose দিয়ে
```bash
# Environment variables সেট করুন
cp .env.example .env.local
# .env.local ফাইল এডিট করুন

# সব সার্ভিস চালু করুন
docker-compose up -d

# লগ দেখুন
docker-compose logs -f
```

## ☁️ DigitalOcean App Platform

### ধাপ ১: অ্যাপ তৈরি করুন
1. DigitalOcean কন্ট্রোল প্যানেলে যান
2. "Apps" সেকশনে যান
3. "Create App" ক্লিক করুন

### ধাপ ২: সোর্স কনফিগার করুন
1. GitHub রিপোজিটরি সংযুক্ত করুন
2. Branch নির্বাচন করুন (main/master)
3. Auto-deploy চালু করুন

### ধাপ ৩: বিল্ড সেটিংস
```yaml
name: friends-organization
services:
- name: web
  source_dir: /
  github:
    repo: your-username/friends-organization
    branch: main
  run_command: npm start
  build_command: npm run build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NEXT_PUBLIC_SUPABASE_URL
    value: your-supabase-url
  - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
    value: your-supabase-anon-key
```

## 🌐 Netlify ডিপ্লয়মেন্ট

### ধাপ ১: Netlify অ্যাকাউন্ট
1. [Netlify.com](https://netlify.com) এ সাইন আপ করুন
2. GitHub সংযুক্ত করুন

### ধাপ ২: সাইট ডিপ্লয় করুন
1. "New site from Git" ক্লিক করুন
2. রিপোজিটরি নির্বাচন করুন
3. বিল্ড সেটিংস:
   - Build command: `npm run build`
   - Publish directory: `.next`

### ধাপ ৩: Environment Variables
Site Settings > Environment Variables এ যান এবং যোগ করুন:
```
NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY
SSLCOMMERZ_STORE_ID
SSLCOMMERZ_STORE_PASSWORD
```

## 🖥️ VPS/Dedicated Server

### Ubuntu/Debian সার্ভারে ডিপ্লয়মেন্ট

#### ধাপ ১: সার্ভার প্রস্তুতি
```bash
# সিস্টেম আপডেট করুন
sudo apt update && sudo apt upgrade -y

# Node.js ইনস্টল করুন
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# PM2 ইনস্টল করুন (প্রোডাকশন ম্যানেজার)
sudo npm install -g pm2

# Nginx ইনস্টল করুন
sudo apt install nginx -y
```

#### ধাপ ২: কোড ডিপ্লয় করুন
```bash
# প্রকল্প ক্লোন করুন
git clone https://github.com/your-username/friends-organization.git
cd friends-organization

# Dependencies ইনস্টল করুন
npm install

# Environment variables সেট করুন
cp .env.example .env.local
nano .env.local

# প্রকল্প বিল্ড করুন
npm run build

# PM2 দিয়ে চালু করুন
pm2 start npm --name "friends-org" -- start
pm2 save
pm2 startup
```

#### ধাপ ৩: Nginx কনফিগারেশন
```bash
# Nginx কনফিগ ফাইল তৈরি করুন
sudo nano /etc/nginx/sites-available/friends-organization
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# সাইট চালু করুন
sudo ln -s /etc/nginx/sites-available/friends-organization /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### ধাপ ৪: SSL সার্টিফিকেট (Let's Encrypt)
```bash
# Certbot ইনস্টল করুন
sudo apt install certbot python3-certbot-nginx -y

# SSL সার্টিফিকেট পান
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal সেট করুন
sudo crontab -e
# এই লাইন যোগ করুন:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring & Maintenance

### PM2 Monitoring
```bash
# অ্যাপ স্ট্যাটাস দেখুন
pm2 status

# লগ দেখুন
pm2 logs friends-org

# অ্যাপ রিস্টার্ট করুন
pm2 restart friends-org

# মেমরি ব্যবহার দেখুন
pm2 monit
```

### Database Backup (Supabase)
```bash
# Supabase CLI ইনস্টল করুন
npm install -g supabase

# ব্যাকআপ নিন
supabase db dump --db-url "your-database-url" > backup.sql
```

### Automated Deployment
GitHub Actions ব্যবহার করে automated deployment সেট করুন:

`.github/workflows/deploy.yml`:
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /path/to/friends-organization
          git pull origin main
          npm install
          npm run build
          pm2 restart friends-org
```

## 🔧 Troubleshooting

### সাধারণ সমস্যা ও সমাধান

#### Build Error
```bash
# Node modules পরিষ্কার করুন
rm -rf node_modules package-lock.json
npm install
```

#### Environment Variables
```bash
# Variables চেক করুন
echo $NEXT_PUBLIC_SUPABASE_URL
```

#### Database Connection
```bash
# Supabase connection টেস্ট করুন
curl -H "apikey: your-anon-key" "your-supabase-url/rest/v1/"
```

#### SSL Issues
```bash
# Nginx কনফিগ টেস্ট করুন
sudo nginx -t

# SSL সার্টিফিকেট রিনিউ করুন
sudo certbot renew
```

## 📞 সাহায্য

সমস্যার সম্মুখীন হলে:
1. GitHub Issues চেক করুন
2. Documentation পড়ুন
3. Community ফোরামে প্রশ্ন করুন

---

**সফল ডিপ্লয়মেন্টের জন্য শুভকামনা!** 🎉
