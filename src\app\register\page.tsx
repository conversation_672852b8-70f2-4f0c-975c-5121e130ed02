'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Upload, User, FileText, Phone, Mail } from 'lucide-react'

interface RegisterForm {
  fullName: string
  email: string
  phone: string
  address: string
  sscRoll: string
  sscYear: string
  sscBoard: string
  monthlyAmount: number
  photo: FileList
  sscMarksheet: FileList
}

export default function Register() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const [marksheetPreview, setMarksheetPreview] = useState<string | null>(null)

  const { register, handleSubmit, formState: { errors }, watch } = useForm<RegisterForm>()

  const onSubmit = async (data: RegisterForm) => {
    setIsSubmitting(true)
    try {
      // Here we would integrate with Supabase to save the data
      console.log('Registration data:', data)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      alert('নিবন্ধন সফল হয়েছে! আপনার আবেদন পর্যালোচনা করা হবে।')
    } catch (error) {
      alert('নিবন্ধনে সমস্যা হয়েছে। আবার চেষ্টা করুন।')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleMarksheetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setMarksheetPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">সদস্য নিবন্ধন</h1>
          <p className="text-gray-600">বন্ধুদের সংস্থায় যোগদানের জন্য নিচের ফর্মটি পূরণ করুন</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Personal Information */}
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-1" />
                পূর্ণ নাম *
              </label>
              <input
                type="text"
                {...register('fullName', { required: 'নাম আবশ্যক' })}
                className="input-field"
                placeholder="আপনার পূর্ণ নাম লিখুন"
              />
              {errors.fullName && (
                <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Mail className="w-4 h-4 inline mr-1" />
                ইমেইল *
              </label>
              <input
                type="email"
                {...register('email', { 
                  required: 'ইমেইল আবশ্যক',
                  pattern: {
                    value: /^\S+@\S+$/i,
                    message: 'সঠিক ইমেইল ঠিকানা দিন'
                  }
                })}
                className="input-field"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Phone className="w-4 h-4 inline mr-1" />
                মোবাইল নম্বর *
              </label>
              <input
                type="tel"
                {...register('phone', { required: 'মোবাইল নম্বর আবশ্যক' })}
                className="input-field"
                placeholder="০১৭xxxxxxxx"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                মাসিক জমার পরিমাণ (টাকা) *
              </label>
              <input
                type="number"
                {...register('monthlyAmount', { 
                  required: 'মাসিক পরিমাণ আবশ্যক',
                  min: { value: 500, message: 'ন্যূনতম ৫০০ টাকা' }
                })}
                className="input-field"
                placeholder="১০০০"
              />
              {errors.monthlyAmount && (
                <p className="text-red-500 text-sm mt-1">{errors.monthlyAmount.message}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ঠিকানা *
            </label>
            <textarea
              {...register('address', { required: 'ঠিকানা আবশ্যক' })}
              className="input-field"
              rows={3}
              placeholder="আপনার সম্পূর্ণ ঠিকানা লিখুন"
            />
            {errors.address && (
              <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>
            )}
          </div>

          {/* SSC Information */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">
              <FileText className="w-5 h-5 inline mr-2" />
              SSC পরীক্ষার তথ্য
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  রোল নম্বর *
                </label>
                <input
                  type="text"
                  {...register('sscRoll', { required: 'রোল নম্বর আবশ্যক' })}
                  className="input-field"
                  placeholder="১২৩৪৫৬"
                />
                {errors.sscRoll && (
                  <p className="text-red-500 text-sm mt-1">{errors.sscRoll.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  পাশের সাল *
                </label>
                <input
                  type="text"
                  {...register('sscYear', { required: 'পাশের সাল আবশ্যক' })}
                  className="input-field"
                  placeholder="২০২০"
                />
                {errors.sscYear && (
                  <p className="text-red-500 text-sm mt-1">{errors.sscYear.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  শিক্ষা বোর্ড *
                </label>
                <select
                  {...register('sscBoard', { required: 'শিক্ষা বোর্ড আবশ্যক' })}
                  className="input-field"
                >
                  <option value="">বোর্ড নির্বাচন করুন</option>
                  <option value="dhaka">ঢাকা</option>
                  <option value="chittagong">চট্টগ্রাম</option>
                  <option value="rajshahi">রাজশাহী</option>
                  <option value="sylhet">সিলেট</option>
                  <option value="barisal">বরিশাল</option>
                  <option value="comilla">কুমিল্লা</option>
                  <option value="jessore">যশোর</option>
                  <option value="dinajpur">দিনাজপুর</option>
                  <option value="mymensingh">ময়মনসিংহ</option>
                </select>
                {errors.sscBoard && (
                  <p className="text-red-500 text-sm mt-1">{errors.sscBoard.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* File Uploads */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">
              <Upload className="w-5 h-5 inline mr-2" />
              ফাইল আপলোড
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  আপনার ছবি * (JPG/PNG, সর্বোচ্চ ২MB)
                </label>
                <input
                  type="file"
                  accept="image/*"
                  {...register('photo', { required: 'ছবি আবশ্যক' })}
                  onChange={handlePhotoChange}
                  className="input-field"
                />
                {errors.photo && (
                  <p className="text-red-500 text-sm mt-1">{errors.photo.message}</p>
                )}
                {photoPreview && (
                  <div className="mt-2">
                    <img src={photoPreview} alt="Preview" className="w-32 h-32 object-cover rounded-lg" />
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SSC মার্কশিট * (PDF/JPG/PNG, সর্বোচ্চ ৫MB)
                </label>
                <input
                  type="file"
                  accept="image/*,.pdf"
                  {...register('sscMarksheet', { required: 'মার্কশিট আবশ্যক' })}
                  onChange={handleMarksheetChange}
                  className="input-field"
                />
                {errors.sscMarksheet && (
                  <p className="text-red-500 text-sm mt-1">{errors.sscMarksheet.message}</p>
                )}
                {marksheetPreview && (
                  <div className="mt-2">
                    <img src={marksheetPreview} alt="Marksheet Preview" className="w-32 h-32 object-cover rounded-lg" />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="border-t pt-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full btn-primary text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'নিবন্ধন করা হচ্ছে...' : 'নিবন্ধন সম্পন্ন করুন'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
