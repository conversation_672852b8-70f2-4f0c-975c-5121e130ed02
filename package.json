{"name": "friends-organization", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.0", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "stripe": "^14.0.0", "react-hook-form": "^7.47.0", "react-dropzone": "^14.2.3", "lucide-react": "^0.292.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "postcss": "^8", "tailwindcss": "^3", "eslint": "^8", "eslint-config-next": "14.0.0"}}