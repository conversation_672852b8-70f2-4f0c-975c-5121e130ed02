<?php
require_once 'config/database.php';

// Check if database connection exists
if (!$db) {
    echo "<div style='text-align: center; padding: 50px;'>";
    echo "<h2>ডেটাবেস কানেকশন সমস্যা</h2>";
    echo "<p>অনুগ্রহ করে প্রথমে ডেটাবেস সেটআপ করুন:</p>";
    echo "<a href='setup.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ডেটাবেস সেটআপ করুন</a>";
    echo "</div>";
    exit;
}

// Handle member approval/rejection
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action']) && isset($_POST['member_id'])) {
        $member_id = (int)$_POST['member_id'];
        $action = $_POST['action'];
        
        if ($action == 'approve') {
            $update_query = "UPDATE members SET status = 'approved' WHERE id = ?";
            $message = "সদস্য অনুমোদিত হয়েছে!";
        } elseif ($action == 'reject') {
            $update_query = "UPDATE members SET status = 'rejected' WHERE id = ?";
            $message = "সদস্যের আবেদন প্রত্যাখ্যান করা হয়েছে!";
        }
        
        if (isset($update_query)) {
            $stmt = $db->prepare($update_query);
            $stmt->execute([$member_id]);
            $success_message = $message;
        }
    }
}

try {
    // Get organization statistics
    $stats_query = "
        SELECT 
            (SELECT COUNT(*) FROM members WHERE status = 'approved') as total_members,
            (SELECT COUNT(*) FROM members WHERE status = 'pending') as pending_members,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed') as total_deposits,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'profit' AND status = 'completed') as total_profits,
            (SELECT COUNT(*) FROM projects WHERE status = 'active') as active_projects,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE) AND YEAR(created_at) = YEAR(CURRENT_DATE)) as monthly_deposits
    ";
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch();

    // Get pending members
    $pending_query = "SELECT * FROM members WHERE status = 'pending' ORDER BY created_at DESC";
    $pending_stmt = $db->prepare($pending_query);
    $pending_stmt->execute();
    $pending_members = $pending_stmt->fetchAll();

    // Get recent transactions
    $transactions_query = "
        SELECT t.*, m.full_name 
        FROM transactions t 
        LEFT JOIN members m ON t.member_id = m.id 
        ORDER BY t.created_at DESC 
        LIMIT 10
    ";
    $transactions_stmt = $db->prepare($transactions_query);
    $transactions_stmt->execute();
    $recent_transactions = $transactions_stmt->fetchAll();

    // Get all members summary
    $members_query = "
        SELECT 
            m.*,
            COALESCE(SUM(CASE WHEN t.type = 'deposit' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as total_deposits,
            COALESCE(SUM(CASE WHEN t.type = 'profit' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as total_profits
        FROM members m 
        LEFT JOIN transactions t ON m.id = t.member_id 
        WHERE m.status = 'approved'
        GROUP BY m.id 
        ORDER BY m.created_at DESC 
        LIMIT 10
    ";
    $members_stmt = $db->prepare($members_query);
    $members_stmt->execute();
    $members_summary = $members_stmt->fetchAll();

} catch (Exception $e) {
    $error_message = "ডেটা লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অ্যাডমিন প্যানেল - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .admin-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
        }
        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .pending-badge {
            animation: pulse 2s infinite;
        }
        .action-btn {
            transition: all 0.2s ease;
        }
        .action-btn:hover {
            transform: scale(1.05);
        }
        .table-row:hover {
            background: #f8fafc;
            transform: translateX(5px);
            transition: all 0.2s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users-cog text-2xl text-purple-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">অ্যাডমিন প্যানেল</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.php" class="text-gray-600 hover:text-purple-600 transition-colors">হোম</a>
                    <a href="dashboard.php" class="text-gray-600 hover:text-purple-600 transition-colors">ড্যাশবোর্ড</a>
                    <a href="admin.php" class="text-purple-600 font-medium">অ্যাডমিন</a>
                    <div class="flex items-center space-x-2">
                        <img src="https://via.placeholder.com/32x32/8b5cf6/ffffff?text=A" alt="Admin" class="w-8 h-8 rounded-full">
                        <span class="text-gray-700 font-medium">অ্যাডমিন</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="admin-gradient text-white py-8">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">অ্যাডমিন ড্যাশবোর্ড</h1>
                    <p class="text-purple-100">সংস্থার সামগ্রিক তথ্য ও ব্যবস্থাপনা</p>
                </div>
                <div class="hidden md:flex space-x-4">
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold"><?php echo $stats['pending_members'] ?? 0; ?></div>
                        <div class="text-sm text-purple-100">অপেক্ষমাণ</div>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold"><?php echo $stats['total_members'] ?? 0; ?></div>
                        <div class="text-sm text-purple-100">মোট সদস্য</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="max-w-7xl mx-auto px-4 mt-4">
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo $success_message; ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Statistics Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">মোট সদস্য</p>
                        <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_members'] ?? 0; ?></p>
                        <p class="text-xs text-blue-600 mt-1">
                            <i class="fas fa-users mr-1"></i>সক্রিয়
                        </p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-users text-2xl text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">অপেক্ষমাণ সদস্য</p>
                        <p class="text-3xl font-bold text-orange-600"><?php echo $stats['pending_members'] ?? 0; ?></p>
                        <p class="text-xs text-orange-600 mt-1 pending-badge">
                            <i class="fas fa-clock mr-1"></i>পর্যালোচনা প্রয়োজন
                        </p>
                    </div>
                    <div class="bg-orange-100 p-3 rounded-full">
                        <i class="fas fa-hourglass-half text-2xl text-orange-600"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">মোট জমা</p>
                        <p class="text-3xl font-bold text-green-600">৳<?php echo number_format($stats['total_deposits'] ?? 0); ?></p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+৮.২%
                        </p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-piggy-bank text-2xl text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">এই মাসের জমা</p>
                        <p class="text-3xl font-bold text-purple-600">৳<?php echo number_format($stats['monthly_deposits'] ?? 0); ?></p>
                        <p class="text-xs text-purple-600 mt-1">
                            <i class="fas fa-calendar mr-1"></i>চলতি মাস
                        </p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-chart-line text-2xl text-purple-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Pending Members -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold flex items-center">
                            <i class="fas fa-user-clock mr-2 text-orange-600"></i>
                            অপেক্ষমাণ সদস্যদের অনুমোদন
                        </h2>
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                            <?php echo count($pending_members); ?>টি আবেদন
                        </span>
                    </div>
                    
                    <?php if (empty($pending_members)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-check-circle text-6xl text-green-300 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">সব আবেদন পর্যালোচনা সম্পন্ন!</h3>
                            <p class="text-gray-500">কোনো অপেক্ষমাণ সদস্য নেই</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b-2 border-gray-200">
                                        <th class="text-left py-3 font-semibold text-gray-700">সদস্যের তথ্য</th>
                                        <th class="text-center py-3 font-semibold text-gray-700">মাসিক জমা</th>
                                        <th class="text-center py-3 font-semibold text-gray-700">আবেদনের তারিখ</th>
                                        <th class="text-center py-3 font-semibold text-gray-700">কার্যক্রম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_members as $member): ?>
                                    <tr class="table-row border-b border-gray-100">
                                        <td class="py-4">
                                            <div class="flex items-center">
                                                <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=<?php echo substr($member['full_name'], 0, 1); ?>" 
                                                     alt="Profile" class="w-10 h-10 rounded-full mr-3">
                                                <div>
                                                    <p class="font-medium text-gray-900"><?php echo htmlspecialchars($member['full_name']); ?></p>
                                                    <p class="text-sm text-gray-500"><?php echo htmlspecialchars($member['email']); ?></p>
                                                    <p class="text-xs text-gray-400"><?php echo htmlspecialchars($member['phone']); ?></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 text-center">
                                            <span class="font-bold text-green-600">৳<?php echo number_format($member['monthly_amount']); ?></span>
                                        </td>
                                        <td class="py-4 text-center text-sm text-gray-600">
                                            <?php echo format_date_bangla($member['created_at']); ?>
                                        </td>
                                        <td class="py-4">
                                            <div class="flex justify-center space-x-2">
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                    <input type="hidden" name="action" value="approve">
                                                    <button type="submit" class="action-btn bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">
                                                        <i class="fas fa-check mr-1"></i>অনুমোদন
                                                    </button>
                                                </form>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                    <input type="hidden" name="action" value="reject">
                                                    <button type="submit" class="action-btn bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-sm font-medium" 
                                                            onclick="return confirm('আপনি কি নিশ্চিত?')">
                                                        <i class="fas fa-times mr-1"></i>প্রত্যাখ্যান
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold mb-6 flex items-center">
                        <i class="fas fa-exchange-alt mr-2 text-blue-600"></i>
                        সাম্প্রতিক লেনদেন
                    </h2>
                    <div class="space-y-3">
                        <?php foreach ($recent_transactions as $transaction): ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3
                                    <?php echo $transaction['type'] == 'deposit' ? 'bg-blue-100 text-blue-600' : 
                                              ($transaction['type'] == 'profit' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'); ?>">
                                    <i class="fas <?php echo $transaction['type'] == 'deposit' ? 'fa-arrow-down' : 'fa-arrow-up'; ?> text-xs"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-sm"><?php echo htmlspecialchars($transaction['full_name'] ?? 'অজানা'); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo htmlspecialchars($transaction['description']); ?></p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-sm <?php echo $transaction['type'] == 'profit' ? 'text-green-600' : 'text-gray-900'; ?>">
                                    ৳<?php echo number_format($transaction['amount']); ?>
                                </p>
                                <p class="text-xs text-gray-500"><?php echo date('d/m/Y', strtotime($transaction['created_at'])); ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-bolt mr-2 text-yellow-600"></i>
                        দ্রুত কাজ
                    </h3>
                    <div class="space-y-3">
                        <button class="w-full btn-primary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-plus mr-2"></i>
                            নতুন প্রকল্প যোগ করুন
                        </button>
                        <button class="w-full btn-secondary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-coins mr-2"></i>
                            লাভ বণ্টন করুন
                        </button>
                        <button class="w-full btn-secondary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-file-alt mr-2"></i>
                            রিপোর্ট তৈরি করুন
                        </button>
                        <button class="w-full btn-secondary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-cog mr-2"></i>
                            সেটিংস
                        </button>
                    </div>
                </div>

                <!-- Top Members -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-star mr-2 text-yellow-600"></i>
                        শীর্ষ সদস্যগণ
                    </h3>
                    <div class="space-y-3">
                        <?php foreach (array_slice($members_summary, 0, 5) as $index => $member): ?>
                        <div class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold mr-3">
                                    <?php echo $index + 1; ?>
                                </div>
                                <div>
                                    <p class="font-medium text-sm"><?php echo htmlspecialchars($member['full_name']); ?></p>
                                    <p class="text-xs text-gray-500">৳<?php echo number_format($member['total_deposits']); ?> জমা</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-green-600 font-medium">৳<?php echo number_format($member['total_profits']); ?></p>
                                <p class="text-xs text-gray-400">লাভ</p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Animate statistics on page load
            const statNumbers = document.querySelectorAll('.stat-card .text-3xl');
            statNumbers.forEach(number => {
                const finalValue = parseInt(number.textContent.replace(/[^\d]/g, ''));
                if (finalValue > 0) {
                    let currentValue = 0;
                    const increment = finalValue / 30;
                    
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        number.textContent = number.textContent.replace(/[\d,]+/, Math.floor(currentValue).toLocaleString());
                    }, 50);
                }
            });

            // Add confirmation for reject buttons
            const rejectButtons = document.querySelectorAll('button[onclick*="confirm"]');
            rejectButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('আপনি কি এই সদস্যের আবেদন প্রত্যাখ্যান করতে চান?')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
</body>
</html>
