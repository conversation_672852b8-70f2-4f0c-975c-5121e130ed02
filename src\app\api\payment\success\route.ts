import { NextRequest, NextResponse } from 'next/server'
import { transactionService } from '@/lib/supabase'
import { SSLCommerzPayment } from '@/lib/payment'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const transactionId = formData.get('tran_id') as string
    const valId = formData.get('val_id') as string
    const amount = formData.get('amount') as string
    const status = formData.get('status') as string

    if (!transactionId || !valId) {
      return NextResponse.json(
        { error: 'Missing transaction data' },
        { status: 400 }
      )
    }

    // Validate payment with SSLCommerz
    const sslcommerz = new SSLCommerzPayment()
    const isValid = await sslcommerz.validatePayment(valId)

    if (!isValid) {
      return NextResponse.json(
        { error: 'Invalid payment' },
        { status: 400 }
      )
    }

    // Update transaction status in database
    const transactions = await transactionService.getAllTransactions()
    const transaction = transactions.find(t => t.transaction_id === transactionId)

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      )
    }

    if (status === 'VALID') {
      await transactionService.updateTransactionStatus(transaction.id, 'completed')
      
      // Redirect to success page
      return NextResponse.redirect(
        new URL(`/payment/success?transactionId=${transactionId}`, request.url)
      )
    } else {
      await transactionService.updateTransactionStatus(transaction.id, 'failed')
      
      // Redirect to failure page
      return NextResponse.redirect(
        new URL(`/payment/failed?transactionId=${transactionId}`, request.url)
      )
    }
  } catch (error) {
    console.error('Payment success handler error:', error)
    return NextResponse.redirect(
      new URL('/payment/failed', request.url)
    )
  }
}

export async function GET(request: NextRequest) {
  // Handle GET requests (some payment gateways use GET for success callback)
  const { searchParams } = new URL(request.url)
  const transactionId = searchParams.get('tran_id')
  const status = searchParams.get('status')

  if (status === 'VALID' && transactionId) {
    return NextResponse.redirect(
      new URL(`/payment/success?transactionId=${transactionId}`, request.url)
    )
  } else {
    return NextResponse.redirect(
      new URL('/payment/failed', request.url)
    )
  }
}
