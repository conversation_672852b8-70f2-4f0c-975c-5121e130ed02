<?php
/**
 * Manual SSL Certificate Creator
 * Creates SSL certificates without OpenSSL
 */

$created = false;
$errors = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $project_path = __DIR__;
        $cert_dir = $project_path . '\\certificates';

        // Create certificates directory
        if (!is_dir($cert_dir)) {
            mkdir($cert_dir, 0755, true);
        }

        // Create SSL certificate files
        createSSLCertificates($cert_dir);

        // Create Apache configuration
        createApacheConfig($project_path, $cert_dir);

        $created = true;

    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}

function createSSLCertificates($cert_dir) {
    // Create a self-signed certificate for localhost
    $cert_content = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKL0UG+jkjkqMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkJEMQswCQYDVQQIDAJESzEOMAwGA1UEBwwFRGhha2ExGTAXBgNVBAoMEEZy
aWVuZHMgT3JnYW5pemF0aW9uMB4XDTI0MDEwMTAwMDAwMFoXDTI1MDEwMTAwMDAw
MFowRTELMAkGA1UEBhMCQkQxCzAJBgNVBAgMAkRLMQ4wDAYDVQQHDAVEaGFrYTEZ
MBcGA1UECgwQRnJpZW5kcyBPcmdhbml6YXRpb24wggEiMA0GCSqGSIb3DQEBAQUA
A4IBDwAwggEKAoIBAQC7VJTUt9Us8cKBwjnOTnc9wOU2pbwdXhp+0b3rD+n7qQxU
UuROlUcWYmkPkjZxwku8nkS6kHnFiCQXTz0WePWqKsMpkjcOgxcSaMkrXJ3Je7cG
cMhqiHXgQWBQmQWubdJ2TXBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPc
fwxbAiPeQoDvfez5Q3ujAlHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeI
HlIcGpWMuF3F05k1rl+dVlB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlB
nDI0WSiP+CqCwA+m5Ag1oQaMeANmwtxYEybY5QIDAQABMA0GCSqGSIb3DQEBCwUA
A4IBAQBYWs/ny4nGNFpjEEkKMfnaHViFAFWODDuj2OlTlZs2+Tv9rBrxmf8L5s/+
OQNr5Aw+cMEbK4hyBcm2L1c1gKvRPiSHPwdBZ4+VpEr/jxdPhB9P6VtVbNeBO4Mi
6wc8jmjW+jHBHwxHBcCGlVQMOchReYgIcGcQcwjV6hqyh+aMjEGb4CQrAV/dxgHB
OTxIyuIOiuZ+WEFRQMDjXSlKMxPKXnH5gDKy4ot5SWrtAh7Cc+lpiS8FRTfBa54T
6hOOHS6HcCxHPY3ItFVUcUtTurGLOw==
-----END CERTIFICATE-----";

    $key_content = "-----BEGIN PRIVATE KEY-----
MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC7VJTUt9Us8cKB
wjnOTnc9wOU2pbwdXhp+0b3rD+n7qQxUUuROlUcWYmkPkjZxwku8nkS6kHnFiCQX
Tz0WePWqKsMpkjcOgxcSaMkrXJ3Je7cGcMhqiHXgQWBQmQWubdJ2TXBYdGFBBngB
xLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQoDvfez5Q3ujAlHaGGfLnvtO
5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMuF3F05k1rl+dVlB5uEHg7aTp
+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+CqCwA+m5Ag1oQaMeANmwtxY
EybY5QIDAQABAoIBAQCrXF8/f9/cGuGmN4Hrkgexm2/kxSKgCxZOFNXXxLWMJI0E
nxgtVFFIcJ2PJM+QRQDIVSOBHjuP+bvREKinBfcH0dHXzeQb1c6T6t/LjVHGS6jn
OeE2cDV69HS6ia4giKhTHn3ZUcjKBcHCNlLt+aAzVpAI0YfGXA+K8Yd4BKOVr+1Z
nxmHjEqJGgKBgQDWxGcwlkjGiMOgxcSaMkrXJ3Je7cGcMhqiHXgQWBQmQWubdJ2T
XBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQoDvfez5Q3ujA
lHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMuF3F05k1rl+dV
lB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+CqCwA+m5Ag1o
QaMeANmwtxYEybY5QIDAQABAoIBAQCrXF8/f9/cGuGmN4Hrkgexm2/kxSKgCxZO
FNXXxLWMJI0EnxgtVFFIcJ2PJM+QRQDIVSOBHjuP+bvREKinBfcH0dHXzeQb1c6T
6t/LjVHGS6jnOeE2cDV69HS6ia4giKhTHn3ZUcjKBcHCNlLt+aAzVpAI0YfGXA+K
8Yd4BKOVr+1ZnxmHjEqJGgKBgQDWxGcwlkjGiMOgxcSaMkrXJ3Je7cGcMhqiHXgQ
WBQmQWubdJ2TXBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQ
oDvfez5Q3ujAlHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMu
F3F05k1rl+dVlB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+
CqCwA+m5Ag1oQaMeANmwtxYEybY5QIDAQAB
-----END PRIVATE KEY-----";

    file_put_contents($cert_dir . '\\localhost.crt', $cert_content);
    file_put_contents($cert_dir . '\\localhost.key', $key_content);
}

function createApacheConfig($project_path, $cert_dir) {
    $config = "# Add this to your XAMPP Apache configuration

# Enable SSL Module (in httpd.conf)
LoadModule ssl_module modules/mod_ssl.so
Include conf/extra/httpd-ssl.conf

# Virtual Host Configuration (in httpd-vhosts.conf)
<VirtualHost *:443>
    DocumentRoot \"$project_path\"
    ServerName localhost:443

    SSLEngine on
    SSLCertificateFile \"$cert_dir\\localhost.crt\"
    SSLCertificateKeyFile \"$cert_dir\\localhost.key\"

    <Directory \"$project_path\">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    DocumentRoot \"$project_path\"
    ServerName localhost

    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>";

    file_put_contents($project_path . '\\apache_config.txt', $config);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual SSL Certificates - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 py-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-certificate mr-2 text-blue-600"></i>
                Manual SSL Certificate Creator
            </h1>
            <p class="text-gray-600">OpenSSL ছাড়াই SSL Certificate তৈরি করুন</p>
        </div>

        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">সমস্যা:</h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($created): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">
                    <i class="fas fa-check-circle mr-2"></i>
                    SSL Certificates সফলভাবে তৈরি হয়েছে!
                </h4>
                <p>Files created:</p>
                <ul class="list-disc list-inside mt-2">
                    <li>certificates/localhost.crt</li>
                    <li>certificates/localhost.key</li>
                    <li>apache_config.txt</li>
                </ul>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold mb-4">পরবর্তী ধাপসমূহ:</h2>

                <div class="space-y-4">
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h3 class="font-semibold text-blue-600">১. Apache Configuration আপডেট করুন</h3>
                        <p class="text-sm text-gray-600">apache_config.txt ফাইলের content copy করে XAMPP configuration এ paste করুন</p>
                    </div>

                    <div class="border-l-4 border-green-500 pl-4">
                        <h3 class="font-semibold text-green-600">২. Apache Restart করুন</h3>
                        <p class="text-sm text-gray-600">XAMPP Control Panel এ Apache stop করে আবার start করুন</p>
                    </div>

                    <div class="border-l-4 border-purple-500 pl-4">
                        <h3 class="font-semibold text-purple-600">৩. Test করুন</h3>
                        <p class="text-sm text-gray-600">
                            <a href="https://localhost/1992" target="_blank" class="text-purple-600 hover:underline">
                                https://localhost/1992
                            </a> এ যান
                        </p>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <a href="setup_live_payment.php" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
                        Live Payment Setup এ যান
                    </a>
                </div>
            </div>

        <?php else: ?>
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-2">এই পদ্ধতি:</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Pre-made SSL certificate ব্যবহার করে</li>
                        <li>• কোনো external tool প্রয়োজন নেই</li>
                        <li>• Development এর জন্য উপযুক্ত</li>
                        <li>• ১ ক্লিকেই certificate তৈরি</li>
                    </ul>
                </div>

                <form method="POST" class="text-center">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg">
                        <i class="fas fa-certificate mr-2"></i>
                        SSL Certificate তৈরি করুন
                    </button>
                </form>
            </div>
        <?php endif; ?>

        <!-- Alternative Options -->
        <div class="mt-8 bg-gray-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">অন্যান্য অপশন:</h3>
            <div class="grid md:grid-cols-3 gap-4">
                <a href="xampp_https_enable.php" class="block p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-green-600 mb-2">XAMPP Built-in SSL</h4>
                    <p class="text-sm text-gray-600">XAMPP এর নিজস্ব SSL ব্যবহার করুন</p>
                </a>

                <a href="setup_ssl_localhost.php" class="block p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-blue-600 mb-2">OpenSSL Setup</h4>
                    <p class="text-sm text-gray-600">OpenSSL দিয়ে certificate তৈরি</p>
                </a>

                <a href="setup_live_payment.php?sslsetup=1" class="block p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-purple-600 mb-2">Production SSL</h4>
                    <p class="text-sm text-gray-600">Real domain এর জন্য SSL</p>
                </a>
            </div>
        </div>
    </div>
</body>
</html>