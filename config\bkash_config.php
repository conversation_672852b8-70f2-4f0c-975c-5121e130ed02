<?php
/**
 * bKash API Configuration
 * Your Personal bKash Account: ***********
 */

// bKash API Credentials (You need to get these from bKash)
define('BKASH_APP_KEY', 'your_app_key_here');
define('BKASH_APP_SECRET', 'your_app_secret_here');
define('BKASH_USERNAME', '***********'); // Your bKash number
define('BKASH_PASSWORD', 'your_bkash_password');

// bKash API URLs
define('BKASH_BASE_URL', 'https://tokenized.pay.bka.sh/v1.2.0-beta');
define('BKASH_TOKEN_URL', BKASH_BASE_URL . '/tokenized/checkout/token/grant');
define('BKASH_CREATE_URL', BKASH_BASE_URL . '/tokenized/checkout/create');
define('BKASH_EXECUTE_URL', BKASH_BASE_URL . '/tokenized/checkout/execute');
define('BKASH_QUERY_URL', BKASH_BASE_URL . '/tokenized/checkout/payment/status');

// For Money Transfer (Send Money)
define('BKASH_TRANSFER_URL', BKASH_BASE_URL . '/tokenized/checkout/payment/transfer');
define('BKASH_BALANCE_URL', BKASH_BASE_URL . '/tokenized/checkout/payment/balance');

// Your bKash Account Settings
define('BKASH_ACCOUNT_NUMBER', '***********');
define('BKASH_ACCOUNT_NAME', 'Your Name Here'); // Your actual name
define('BKASH_ACCOUNT_TYPE', 'personal'); // personal or merchant

// Transaction Limits
define('BKASH_MIN_TRANSFER', 10);
define('BKASH_MAX_TRANSFER', 25000);
define('BKASH_DAILY_LIMIT', 100000);
define('BKASH_MONTHLY_LIMIT', 500000);

// Callback URLs
define('BKASH_SUCCESS_URL', APP_URL . '/bkash_success.php');
define('BKASH_FAIL_URL', APP_URL . '/bkash_fail.php');
define('BKASH_CANCEL_URL', APP_URL . '/bkash_cancel.php');

// Fee Structure
$bkash_fees = [
    'send_money' => [
        'min_amount' => 10,
        'max_amount' => 25000,
        'fee_percentage' => 1.85,
        'fixed_fee' => 5
    ],
    'cash_out' => [
        'min_amount' => 50,
        'max_amount' => 25000,
        'fee_percentage' => 1.85,
        'fixed_fee' => 5
    ],
    'payment' => [
        'min_amount' => 10,
        'max_amount' => 25000,
        'fee_percentage' => 1.85,
        'fixed_fee' => 5
    ]
];

/**
 * Calculate bKash transaction fee
 */
function calculateBkashFee($amount, $type = 'send_money') {
    global $bkash_fees;
    
    if (!isset($bkash_fees[$type])) {
        return 0;
    }
    
    $fee_config = $bkash_fees[$type];
    $fee = ($amount * $fee_config['fee_percentage'] / 100) + $fee_config['fixed_fee'];
    
    return round($fee, 2);
}

/**
 * Get net amount after fee deduction
 */
function getBkashNetAmount($amount, $type = 'send_money') {
    $fee = calculateBkashFee($amount, $type);
    return $amount - $fee;
}

/**
 * Validate bKash transaction amount
 */
function validateBkashAmount($amount, $type = 'send_money') {
    global $bkash_fees;
    
    if (!isset($bkash_fees[$type])) {
        return false;
    }
    
    $limits = $bkash_fees[$type];
    return ($amount >= $limits['min_amount'] && $amount <= $limits['max_amount']);
}

/**
 * Format bKash mobile number
 */
function formatBkashNumber($number) {
    // Remove any non-digit characters
    $number = preg_replace('/[^0-9]/', '', $number);
    
    // Add country code if not present
    if (strlen($number) == 11 && substr($number, 0, 2) == '01') {
        $number = '88' . $number;
    } elseif (strlen($number) == 11) {
        $number = '88' . $number;
    }
    
    return $number;
}

/**
 * Generate bKash transaction reference
 */
function generateBkashReference() {
    return 'BK' . date('YmdHis') . rand(1000, 9999);
}

/**
 * Log bKash transaction
 */
function logBkashTransaction($data) {
    global $db;
    
    try {
        $query = "INSERT INTO bkash_transactions (
            reference_id, transaction_type, amount, fee, 
            sender_number, receiver_number, status, 
            bkash_transaction_id, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['reference_id'],
            $data['transaction_type'],
            $data['amount'],
            $data['fee'],
            $data['sender_number'],
            $data['receiver_number'],
            $data['status'],
            $data['bkash_transaction_id'] ?? null
        ]);
        
        return $db->lastInsertId();
        
    } catch (Exception $e) {
        error_log("bKash transaction logging failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get bKash account balance
 */
function getBkashBalance() {
    // This would require bKash API integration
    // For now, return a placeholder
    return [
        'balance' => 0,
        'status' => 'unavailable',
        'message' => 'bKash API integration required'
    ];
}

/**
 * Send money via bKash
 */
function sendMoneyBkash($receiver_number, $amount, $reference = null) {
    // This would require bKash API integration
    // For now, return a placeholder response
    
    $reference = $reference ?: generateBkashReference();
    $fee = calculateBkashFee($amount, 'send_money');
    
    // Log the transaction attempt
    logBkashTransaction([
        'reference_id' => $reference,
        'transaction_type' => 'send_money',
        'amount' => $amount,
        'fee' => $fee,
        'sender_number' => BKASH_ACCOUNT_NUMBER,
        'receiver_number' => $receiver_number,
        'status' => 'pending'
    ]);
    
    return [
        'success' => false,
        'message' => 'bKash API integration required for live transactions',
        'reference' => $reference,
        'amount' => $amount,
        'fee' => $fee,
        'receiver' => $receiver_number
    ];
}

/**
 * Check bKash transaction status
 */
function checkBkashTransactionStatus($reference_id) {
    global $db;
    
    try {
        $query = "SELECT * FROM bkash_transactions WHERE reference_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$reference_id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("bKash status check failed: " . $e->getMessage());
        return false;
    }
}
?>
