# রিয়েল পেমেন্ট গেটওয়ে সেটআপ গাইড

## 🏦 SSLCommerz রেজিস্ট্রেশন (বাংলাদেশের জন্য)

### ধাপ ১: SSLCommerz অ্যাকাউন্ট তৈরি করুন

1. **ওয়েবসাইটে যান:** [https://www.sslcommerz.com](https://www.sslcommerz.com)
2. **"Merchant Registration" ক্লিক করুন**
3. **ব্যবসার তথ্য দিন:**
   - ব্যবসার নাম: বন্ধুদের সংস্থা
   - ব্যবসার ধরন: Financial Services
   - ওয়েবসাইট: আপনার ডোমেইন
   - যোগাযোগের তথ্য

### ধাপ ২: প্রয়োজনীয় ডকুমেন্ট

**ব্যক্তিগত অ্যাকাউন্টের জন্য:**
- জাতীয় পরিচয়পত্র
- ব্যাংক স্টেটমেন্ট
- ব্যবসার লাইসেন্স (যদি থাকে)

**কোম্পানি অ্যাকাউন্টের জন্য:**
- ট্রেড লাইসেন্স
- TIN সার্টিফিকেট
- ব্যাংক সার্টিফিকেট
- পরিচালকদের জাতীয় পরিচয়পত্র

### ধাপ ৩: ব্যাংক অ্যাকাউন্ট সেটআপ

1. **সাপোর্টেড ব্যাংক নির্বাচন করুন:**
   - Dutch Bangla Bank (DBBL)
   - BRAC Bank
   - City Bank
   - Eastern Bank
   - Islami Bank Bangladesh

2. **Settlement অ্যাকাউন্ট:**
   - আপনার ব্যাংক অ্যাকাউন্ট যেখানে পেমেন্ট জমা হবে
   - অ্যাকাউন্ট নম্বর ও রাউটিং নম্বর প্রয়োজন

### ধাপ ৪: API Credentials পান

**Sandbox (টেস্ট) Credentials:**
```
Store ID: testbox
Store Password: qwerty
```

**Live Credentials (অনুমোদনের পর):**
- আপনার নিজস্ব Store ID
- আপনার নিজস্ব Store Password

## 💳 পেমেন্ট মেথড সেটআপ

### মোবাইল ব্যাংকিং
- **bKash:** ন্যূনতম ১০ টাকা, সর্বোচ্চ ২৫,০০০ টাকা
- **Nagad:** ন্যূনতম ১০ টাকা, সর্বোচ্চ ২৫,০০০ টাকা
- **Rocket:** ন্যূনতম ১০ টাকা, সর্বোচ্চ ২৫,০০০ টাকা

### কার্ড পেমেন্ট
- **Visa/Mastercard:** ন্যূনতম ১০০ টাকা, সর্বোচ্চ ৫,০০,০০০ টাকা
- **American Express:** ন্যূনতম ১০০ টাকা

### নেট ব্যাংকিং
- সকল প্রধান ব্যাংকের সাপোর্ট
- ন্যূনতম ১০০ টাকা

## 🔧 কনফিগারেশন

### config/database.php ফাইলে আপডেট করুন:

```php
// Live Environment এর জন্য
define('SSLCOMMERZ_STORE_ID', 'your_actual_store_id');
define('SSLCOMMERZ_STORE_PASSWORD', 'your_actual_store_password');
define('SSLCOMMERZ_IS_LIVE', true); // Live এর জন্য true করুন

// URLs আপডেট করুন
define('APP_URL', 'https://yourdomain.com');
```

### SSL Certificate প্রয়োজন

**Live পেমেন্টের জন্য আপনার ওয়েবসাইটে SSL Certificate থাকতে হবে:**
- Let's Encrypt (ফ্রি)
- Cloudflare SSL
- Paid SSL Certificate

## 💰 ফি স্ট্রাকচার

### SSLCommerz ফি (আনুমানিক):
- **মোবাইল ব্যাংকিং:** ১.৫% + ৫ টাকা
- **কার্ড পেমেন্ট:** ২.৯% + ৫ টাকা
- **নেট ব্যাংকিং:** ১.৮% + ৫ টাকা

### Settlement সময়:
- **T+1:** পরের কার্যদিবস
- **T+2:** দুই কার্যদিবস পর (স্ট্যান্ডার্ড)

## 🧪 টেস্টিং

### Sandbox টেস্ট করুন:

1. **টেস্ট কার্ড নম্বর:**
   ```
   Visa: ****************
   Mastercard: ****************
   CVV: 123
   Expiry: যেকোনো ভবিষ্যতের তারিখ
   ```

2. **টেস্ট মোবাইল ব্যাংকিং:**
   - bKash: 01XXXXXXXXX
   - PIN: 12345

3. **টেস্ট পরিমাণ:**
   - সফল: ১০, ১০০, ১০০০ টাকা
   - ব্যর্থ: ১৫, ২৫ টাকা

## 🚀 Go Live প্রক্রিয়া

### ধাপ ১: ডকুমেন্ট জমা দিন
- সকল প্রয়োজনীয় কাগজপত্র
- ব্যাংক অ্যাকাউন্ট ভেরিফিকেশন

### ধাপ ২: SSLCommerz রিভিউ
- ৩-৭ কার্যদিবস সময় লাগে
- অতিরিক্ত তথ্যের প্রয়োজন হতে পারে

### ধাপ ৩: Live Credentials পান
- Store ID ও Password
- Webhook URLs সেটআপ

### ধাপ ৪: Production এ Deploy
```php
define('SSLCOMMERZ_IS_LIVE', true);
```

## 🔒 নিরাপত্তা

### গুরুত্বপূর্ণ নিরাপত্তা ব্যবস্থা:

1. **SSL Certificate:** বাধ্যতামূলক
2. **Webhook Validation:** সব response verify করুন
3. **Amount Validation:** ডাবল চেক করুন
4. **Transaction Logging:** সব লেনদেন লগ রাখুন
5. **Error Handling:** Proper error management

### Webhook Security:
```php
// IPN validation
$hash = md5($store_passwd.$post_data['val_id'].$post_data['amount'].$post_data['tran_id']);
if ($hash !== $post_data['verify_sign']) {
    // Invalid request
    exit('Invalid signature');
}
```

## 📞 সাহায্য ও সাপোর্ট

### SSLCommerz সাপোর্ট:
- **ফোন:** +৮৮০-২-৫৫০৪৮৮৮৮
- **ইমেইল:** <EMAIL>
- **ডকুমেন্টেশন:** [https://developer.sslcommerz.com](https://developer.sslcommerz.com)

### সাধারণ সমস্যা ও সমাধান:

**সমস্যা:** "Store not found"
**সমাধান:** Store ID ও Password চেক করুন

**সমস্যা:** "Invalid amount"
**সমাধান:** Amount format ও minimum limit চেক করুন

**সমস্যা:** "SSL required"
**সমাধান:** HTTPS ব্যবহার করুন

## 🎯 বিকল্প পেমেন্ট গেটওয়ে

### অন্যান্য অপশন:

1. **PortWallet:**
   - কম ফি
   - দ্রুত settlement

2. **aamarPay:**
   - সহজ integration
   - ভাল customer support

3. **Shurjopay:**
   - বাংলাদেশি কোম্পানি
   - competitive rates

4. **Stripe (আন্তর্জাতিক):**
   - গ্লোবাল পেমেন্ট
   - বাংলাদেশে সীমিত

## ✅ চেকলিস্ট

### Go Live এর আগে:

- [ ] SSL Certificate ইনস্টল করা
- [ ] Live credentials পাওয়া
- [ ] Webhook URLs সেটআপ করা
- [ ] Error handling টেস্ট করা
- [ ] Transaction logging চালু করা
- [ ] Email notifications সেটআপ করা
- [ ] Backup payment method রাখা
- [ ] Customer support তৈরি করা

---

**সফল পেমেন্ট ইন্টিগ্রেশনের জন্য শুভকামনা!** 🎉
