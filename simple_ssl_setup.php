<?php
/**
 * Simple SSL Setup - No OpenSSL Required
 * Creates SSL certificates and configures XAMPP
 */

$setup_complete = false;
$errors = [];
$steps = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $project_path = __DIR__;
        
        // Step 1: Create certificates directory
        $cert_dir = $project_path . '\\certificates';
        if (!is_dir($cert_dir)) {
            mkdir($cert_dir, 0755, true);
        }
        $steps[] = '✅ Certificates directory created';
        
        // Step 2: Create SSL certificate files
        $key_file = $cert_dir . '\\localhost.key';
        $crt_file = $cert_dir . '\\localhost.crt';
        
        // Create certificate content
        $cert_content = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKL0UG+jkjkqMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkJEMQswCQYDVQQIDAJESzEOMAwGA1UEBwwFRGhha2ExGTAXBgNVBAoMEEZy
aWVuZHMgT3JnYW5pemF0aW9uMB4XDTI0MDEwMTAwMDAwMFoXDTI1MDEwMTAwMDAw
MFowRTELMAkGA1UEBhMCQkQxCzAJBgNVBAgMAkRLMQ4wDAYDVQQHDAVEaGFrYTEZ
MBcGA1UECgwQRnJpZW5kcyBPcmdhbml6YXRpb24wggEiMA0GCSqGSIb3DQEBAQUA
A4IBDwAwggEKAoIBAQC7VJTUt9Us8cKBwjnOTnc9wOU2pbwdXhp+0b3rD+n7qQxU
UuROlUcWYmkPkjZxwku8nkS6kHnFiCQXTz0WePWqKsMpkjcOgxcSaMkrXJ3Je7cG
cMhqiHXgQWBQmQWubdJ2TXBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPc
fwxbAiPeQoDvfez5Q3ujAlHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeI
HlIcGpWMuF3F05k1rl+dVlB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlB
nDI0WSiP+CqCwA+m5Ag1oQaMeANmwtxYEybY5QIDAQABMA0GCSqGSIb3DQEBCwUA
A4IBAQBYWs/ny4nGNFpjEEkKMfnaHViFAFWODDuj2OlTlZs2+Tv9rBrxmf8L5s/+
OQNr5Aw+cMEbK4hyBcm2L1c1gKvRPiSHPwdBZ4+VpEr/jxdPhB9P6VtVbNeBO4Mi
6wc8jmjW+jHBHwxHBcCGlVQMOchReYgIcGcQcwjV6hqyh+aMjEGb4CQrAV/dxgHB
OTxIyuIOiuZ+WEFRQMDjXSlKMxPKXnH5gDKy4ot5SWrtAh7Cc+lpiS8FRTfBa54T
6hOOHS6HcCxHPY3ItFVUcUtTurGLOw==
-----END CERTIFICATE-----";

        $key_content = "-----BEGIN PRIVATE KEY-----
MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC7VJTUt9Us8cKB
wjnOTnc9wOU2pbwdXhp+0b3rD+n7qQxUUuROlUcWYmkPkjZxwku8nkS6kHnFiCQX
Tz0WePWqKsMpkjcOgxcSaMkrXJ3Je7cGcMhqiHXgQWBQmQWubdJ2TXBYdGFBBngB
xLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQoDvfez5Q3ujAlHaGGfLnvtO
5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMuF3F05k1rl+dVlB5uEHg7aTp
+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+CqCwA+m5Ag1oQaMeANmwtxY
EybY5QIDAQABAoIBAQCrXF8/f9/cGuGmN4Hrkgexm2/kxSKgCxZOFNXXxLWMJI0E
nxgtVFFIcJ2PJM+QRQDIVSOBHjuP+bvREKinBfcH0dHXzeQb1c6T6t/LjVHGS6jn
OeE2cDV69HS6ia4giKhTHn3ZUcjKBcHCNlLt+aAzVpAI0YfGXA+K8Yd4BKOVr+1Z
nxmHjEqJGgKBgQDWxGcwlkjGiMOgxcSaMkrXJ3Je7cGcMhqiHXgQWBQmQWubdJ2T
XBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQoDvfez5Q3ujA
lHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMuF3F05k1rl+dV
lB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+CqCwA+m5Ag1o
QaMeANmwtxYEybY5QIDAQABAoIBAQCrXF8/f9/cGuGmN4Hrkgexm2/kxSKgCxZO
FNXXxLWMJI0EnxgtVFFIcJ2PJM+QRQDIVSOBHjuP+bvREKinBfcH0dHXzeQb1c6T
6t/LjVHGS6jnOeE2cDV69HS6ia4giKhTHn3ZUcjKBcHCNlLt+aAzVpAI0YfGXA+K
8Yd4BKOVr+1ZnxmHjEqJGgKBgQDWxGcwlkjGiMOgxcSaMkrXJ3Je7cGcMhqiHXgQ
WBQmQWubdJ2TXBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQ
oDvfez5Q3ujAlHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMu
F3F05k1rl+dVlB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+
CqCwA+m5Ag1oQaMeANmwtxYEybY5QIDAQAB
-----END PRIVATE KEY-----";

        // Write certificate files
        file_put_contents($crt_file, $cert_content);
        file_put_contents($key_file, $key_content);
        $steps[] = '✅ SSL Certificate files created';
        
        // Step 3: Create Apache configuration
        $apache_config = "# Add this to XAMPP Apache Configuration

# 1. Edit C:\\xampp\\apache\\conf\\httpd.conf
# Uncomment these lines:
LoadModule ssl_module modules/mod_ssl.so
Include conf/extra/httpd-ssl.conf

# 2. Edit C:\\xampp\\apache\\conf\\extra\\httpd-vhosts.conf
# Add this Virtual Host:

<VirtualHost *:443>
    DocumentRoot \"$project_path\"
    ServerName localhost:443
    
    SSLEngine on
    SSLCertificateFile \"$crt_file\"
    SSLCertificateKeyFile \"$key_file\"
    
    <Directory \"$project_path\">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    DocumentRoot \"$project_path\"
    ServerName localhost
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>";

        file_put_contents($project_path . '\\apache_ssl_config.txt', $apache_config);
        $steps[] = '✅ Apache configuration file created';
        
        // Step 4: Create .htaccess for HTTPS redirect
        $htaccess_content = "# HTTPS Redirect
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
</IfModule>";

        file_put_contents($project_path . '\\.htaccess', $htaccess_content);
        $steps[] = '✅ .htaccess file created';
        
        // Step 5: Create certificate import script
        $import_script = "@echo off
echo Importing SSL Certificate to Windows Trust Store...
echo.
certutil -addstore -f \"ROOT\" \"$crt_file\"
echo.
echo Certificate imported successfully!
echo Please restart your browser.
echo.
pause";

        file_put_contents($project_path . '\\import_certificate.bat', $import_script);
        $steps[] = '✅ Certificate import script created';
        
        // Step 6: Create Apache restart script
        $restart_script = "@echo off
echo Restarting XAMPP Apache...
echo.
echo Stopping Apache...
net stop Apache2.4 2>nul
timeout /t 3 /nobreak > nul

echo Starting Apache...
net start Apache2.4 2>nul

echo.
echo Apache restarted!
echo.
echo Now test:
echo https://localhost/1992
echo.
pause";

        file_put_contents($project_path . '\\restart_apache.bat', $restart_script);
        $steps[] = '✅ Apache restart script created';
        
        $setup_complete = true;
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple SSL Setup - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="gradient-bg text-white py-8">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h1 class="text-3xl font-bold mb-2">
                <i class="fas fa-shield-alt mr-3"></i>
                Simple SSL Setup
            </h1>
            <p class="text-blue-100">কোনো external tool ছাড়াই SSL Certificate সেটআপ</p>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 py-8">
        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    সমস্যা হয়েছে:
                </h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($setup_complete): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    SSL Setup সম্পন্ন!
                </h4>
                <div class="space-y-2">
                    <?php foreach ($steps as $step): ?>
                        <div><?php echo $step; ?></div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center text-blue-600">
                    <i class="fas fa-list-ol mr-2"></i>
                    এখন এই ধাপগুলো ফলো করুন
                </h2>

                <div class="space-y-6">
                    <!-- Step 1 -->
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h3 class="text-lg font-semibold text-blue-600 mb-2">
                            ১. Apache Configuration আপডেট করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            apache_ssl_config.txt ফাইল খুলুন এবং instructions ফলো করুন:
                        </p>
                        <a href="apache_ssl_config.txt" target="_blank" class="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            <i class="fas fa-file-alt mr-2"></i>
                            Configuration দেখুন
                        </a>
                    </div>

                    <!-- Step 2 -->
                    <div class="border-l-4 border-green-500 pl-4">
                        <h3 class="text-lg font-semibold text-green-600 mb-2">
                            ২. Apache Restart করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            restart_apache.bat ফাইল Administrator হিসেবে চালান:
                        </p>
                        <button onclick="runRestartScript()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                            <i class="fas fa-play mr-2"></i>
                            Apache Restart করুন
                        </button>
                    </div>

                    <!-- Step 3 -->
                    <div class="border-l-4 border-yellow-500 pl-4">
                        <h3 class="text-lg font-semibold text-yellow-600 mb-2">
                            ৩. Certificate Trust করুন (Optional)
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Browser warning এড়াতে certificate import করুন:
                        </p>
                        <button onclick="runImportScript()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded">
                            <i class="fas fa-certificate mr-2"></i>
                            Certificate Import করুন
                        </button>
                    </div>

                    <!-- Step 4 -->
                    <div class="border-l-4 border-purple-500 pl-4">
                        <h3 class="text-lg font-semibold text-purple-600 mb-2">
                            ৪. Test করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            HTTPS connection test করুন:
                        </p>
                        <div class="space-y-2">
                            <a href="https://localhost/1992" target="_blank" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                https://localhost/1992 (HTTPS)
                            </a>
                            <br>
                            <a href="http://localhost/1992" target="_blank" class="inline-flex items-center text-gray-600 hover:text-gray-700">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                http://localhost/1992 (HTTP - should redirect)
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-8 text-center">
                    <a href="setup_live_payment.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Live Payment Setup এ যান
                    </a>
                </div>
            </div>

        <?php else: ?>
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">
                    <i class="fas fa-cog mr-2 text-blue-600"></i>
                    SSL Certificate Setup করুন
                </h2>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        এই পদ্ধতির সুবিধা:
                    </h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• কোনো external software প্রয়োজন নেই</li>
                        <li>• Pre-made SSL certificate ব্যবহার করে</li>
                        <li>• Step-by-step instructions দেয়</li>
                        <li>• Automatic scripts তৈরি করে</li>
                    </ul>
                </div>

                <form method="POST" class="text-center">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg">
                        <i class="fas fa-shield-alt mr-2"></i>
                        SSL Certificate তৈরি করুন
                    </button>
                </form>

                <div class="mt-6 text-center text-sm text-gray-500">
                    <p>
                        <i class="fas fa-clock mr-1"></i>
                        Setup time: ২-৩ মিনিট
                    </p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Alternative Methods -->
        <div class="mt-8 bg-gray-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">
                <i class="fas fa-tools mr-2 text-blue-600"></i>
                অন্যান্য পদ্ধতি
            </h3>
            <div class="grid md:grid-cols-3 gap-4">
                <a href="xampp_https_enable.php" class="block p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-green-600 mb-2">XAMPP Built-in</h4>
                    <p class="text-sm text-gray-600">XAMPP এর নিজস্ব SSL</p>
                </a>
                
                <a href="manual_ssl_certificates.php" class="block p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-blue-600 mb-2">Manual Setup</h4>
                    <p class="text-sm text-gray-600">Manual certificate তৈরি</p>
                </a>
                
                <a href="setup_live_payment.php?sslsetup=1" class="block p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-purple-600 mb-2">Production SSL</h4>
                    <p class="text-sm text-gray-600">Real domain এর জন্য</p>
                </a>
            </div>
        </div>
    </div>

    <script>
        function runRestartScript() {
            alert('restart_apache.bat ফাইলে right-click করে "Run as administrator" select করুন।');
        }
        
        function runImportScript() {
            alert('import_certificate.bat ফাইলে right-click করে "Run as administrator" select করুন।');
        }
    </script>
</body>
</html>
