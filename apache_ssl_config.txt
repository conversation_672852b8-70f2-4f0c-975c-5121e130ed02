# Add this to XAMPP Apache Configuration

# 1. Edit C:\xampp\apache\conf\httpd.conf
# Uncomment these lines:
LoadModule ssl_module modules/mod_ssl.so
Include conf/extra/httpd-ssl.conf

# 2. Edit C:\xampp\apache\conf\extra\httpd-vhosts.conf
# Add this Virtual Host:

<VirtualHost *:443>
    DocumentRoot "D:\xampp\htdocs\1992"
    ServerName localhost:443
    
    SSLEngine on
    SSLCertificateFile "D:\xampp\htdocs\1992\certificates\localhost.crt"
    SSLCertificateKeyFile "D:\xampp\htdocs\1992\certificates\localhost.key"
    
    <Directory "D:\xampp\htdocs\1992">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    DocumentRoot "D:\xampp\htdocs\1992"
    ServerName localhost
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>