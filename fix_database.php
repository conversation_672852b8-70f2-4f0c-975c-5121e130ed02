<?php
require_once 'config/database.php';

echo "<h2>ডেটাবেস ফিক্স স্ক্রিপ্ট</h2>";

try {
    // Add missing columns
    echo "<p>Missing columns যোগ করা হচ্ছে...</p>";
    
    // Add updated_at to transactions table
    $db->exec("ALTER TABLE transactions ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
    echo "<p style='color: green;'>✓ transactions টেবিলে updated_at কলাম যোগ করা হয়েছে</p>";
    
    // Add payment_logs table if not exists
    $payment_logs_sql = "
    CREATE TABLE IF NOT EXISTS payment_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transaction_id VARCHAR(100) NOT NULL,
        status ENUM('initiated', 'completed', 'failed', 'cancelled') NOT NULL,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    )";
    
    $db->exec($payment_logs_sql);
    echo "<p style='color: green;'>✓ payment_logs টেবিল তৈরি/চেক করা হয়েছে</p>";
    
    // Update existing transactions to add updated_at values
    $db->exec("UPDATE transactions SET updated_at = created_at WHERE updated_at IS NULL");
    echo "<p style='color: green;'>✓ existing transactions এ updated_at ভ্যালু সেট করা হয়েছে</p>";
    
    // Check if all required columns exist
    $check_query = "SHOW COLUMNS FROM transactions";
    $stmt = $db->prepare($check_query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>Transactions টেবিলের কলামসমূহ:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li style='color: blue;'>$column</li>";
    }
    echo "</ul>";
    
    // Test a simple query
    $test_query = "SELECT COUNT(*) as total FROM transactions";
    $stmt = $db->prepare($test_query);
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p style='color: green;'>✓ টেস্ট কোয়েরি সফল! মোট লেনদেন: {$result['total']}টি</p>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>ডেটাবেস ফিক্স সম্পন্ন! ✅</h3>";
    echo "<p>এখন আপনি পেমেন্ট সিস্টেম ব্যবহার করতে পারেন।</p>";
    echo "<p><a href='payment.php' style='color: #007bff;'>পেমেন্ট পেজে যান</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>সমস্যা সমাধানের উপায়:</h4>";
    echo "<ol>";
    echo "<li>XAMPP এ MySQL চালু আছে কিনা চেক করুন</li>";
    echo "<li>setup.php আবার চালান</li>";
    echo "<li>phpMyAdmin এ manually কলাম যোগ করুন</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডেটাবেস ফিক্স - বন্ধুদের সংস্থা</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- PHP output will be displayed here -->
    </div>
</body>
</html>
