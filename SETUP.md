# সেটআপ গাইড - বন্ধুদের সংস্থা

## 🚀 দ্রুত শুরু

### প্রয়োজনীয় সফটওয়্যার ইনস্টল করুন

#### ১. Node.js ইনস্টল করুন
- [Node.js অফিসিয়াল সাইট](https://nodejs.org/) থেকে LTS ভার্সন ডাউনলোড করুন
- Windows এর জন্য `.msi` ফাইল ডাউনলোড করুন
- ইনস্টল করার পর Command Prompt/PowerShell খুলে টেস্ট করুন:
```bash
node --version
npm --version
```

#### ২. Git ইনস্টল করুন (ঐচ্ছিক)
- [Git অফিসিয়াল সাইট](https://git-scm.com/) থেকে ডাউনলোড করুন

### প্রকল্প সেটআপ

#### ১. Dependencies ইনস্টল করুন
```bash
npm install
```

#### ২. Environment Variables সেটআপ করুন
```bash
# .env.example ফাইল কপি করুন
copy .env.example .env.local
```

`.env.local` ফাইল এডিট করে আপনার তথ্য দিন:
```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

#### ৩. Development Server চালু করুন
```bash
npm run dev
```

ব্রাউজারে `http://localhost:3000` এ যান।

## 🗄️ Supabase সেটআপ

### ১. Supabase অ্যাকাউন্ট তৈরি করুন
- [Supabase.com](https://supabase.com) এ যান
- "Start your project" ক্লিক করুন
- GitHub দিয়ে সাইন আপ করুন

### ২. নতুন প্রকল্প তৈরি করুন
- "New Project" ক্লিক করুন
- প্রকল্পের নাম দিন: "Friends Organization"
- Database Password সেট করুন
- Region নির্বাচন করুন (Singapore সবচেয়ে কাছে)

### ৩. Database সেটআপ করুন
- Supabase Dashboard এ যান
- "SQL Editor" এ ক্লিক করুন
- `database/setup.sql` ফাইলের কন্টেন্ট কপি করে পেস্ট করুন
- "Run" ক্লিক করুন

### ৪. API Keys সংগ্রহ করুন
- "Settings" > "API" এ যান
- `URL` এবং `anon public` key কপি করুন
- `.env.local` ফাইলে পেস্ট করুন

## 💳 পেমেন্ট গেটওয়ে সেটআপ

### SSLCommerz (বাংলাদেশের জন্য)
1. [SSLCommerz](https://www.sslcommerz.com/) এ যান
2. "Merchant Registration" ক্লিক করুন
3. ব্যবসার তথ্য দিয়ে রেজিস্ট্রেশন করুন
4. Sandbox credentials পান
5. `.env.local` এ যোগ করুন:
```env
SSLCOMMERZ_STORE_ID=your-store-id
SSLCOMMERZ_STORE_PASSWORD=your-store-password
SSLCOMMERZ_IS_LIVE=false
```

### Stripe (আন্তর্জাতিক)
1. [Stripe](https://stripe.com/) এ অ্যাকাউন্ট তৈরি করুন
2. Dashboard থেকে API keys নিন
3. `.env.local` এ যোগ করুন:
```env
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

## 🔧 ডেভেলপমেন্ট কমান্ড

```bash
# Development server চালু করুন
npm run dev

# Production build তৈরি করুন
npm run build

# Production server চালু করুন
npm start

# Code linting
npm run lint

# Type checking
npx tsc --noEmit
```

## 📁 প্রকল্প স্ট্রাকচার

```
friends-organization/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── page.tsx        # হোম পেজ
│   │   ├── register/       # নিবন্ধন
│   │   ├── dashboard/      # ড্যাশবোর্ড
│   │   ├── payment/        # পেমেন্ট
│   │   ├── admin/          # অ্যাডমিন প্যানেল
│   │   └── api/            # API Routes
│   ├── components/         # React Components
│   └── lib/               # Utility functions
├── database/              # Database scripts
├── public/               # Static files
├── package.json          # Dependencies
├── tailwind.config.js    # Tailwind CSS config
├── next.config.js        # Next.js config
└── README.md            # Documentation
```

## 🐛 সমস্যা সমাধান

### Node.js ইনস্টল সমস্যা
- Windows Defender বা Antivirus বন্ধ করুন
- Administrator হিসেবে ইনস্টল করুন
- PATH environment variable চেক করুন

### npm install সমস্যা
```bash
# Cache পরিষ্কার করুন
npm cache clean --force

# node_modules ডিলিট করে আবার ইনস্টল করুন
rmdir /s node_modules
del package-lock.json
npm install
```

### Port 3000 ব্যবহৃত
```bash
# অন্য port ব্যবহার করুন
npm run dev -- -p 3001
```

### Supabase Connection Error
- URL এবং API key সঠিক আছে কিনা চেক করুন
- Internet connection চেক করুন
- Supabase service status চেক করুন

## 📱 মোবাইল টেস্টিং

### Local Network এ টেস্ট করুন
1. আপনার কম্পিউটারের IP address বের করুন:
```bash
ipconfig
```

2. Mobile browser এ যান:
```
http://YOUR-IP-ADDRESS:3000
```

### Responsive Design টেস্ট
- Chrome DevTools এ mobile view চেক করুন
- বিভিন্ন screen size টেস্ট করুন

## 🔐 নিরাপত্তা

### Environment Variables
- `.env.local` ফাইল কখনো Git এ commit করবেন না
- Production এ strong passwords ব্যবহার করুন
- API keys নিয়মিত rotate করুন

### Database Security
- Row Level Security (RLS) চালু রাখুন
- Backup নিয়মিত নিন
- Access logs monitor করুন

## 📞 সাহায্য

### Documentation
- [Next.js Docs](https://nextjs.org/docs)
- [Supabase Docs](https://supabase.com/docs)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)

### Community
- [Next.js Discord](https://discord.gg/nextjs)
- [Supabase Discord](https://discord.supabase.com/)

### Issues
- GitHub Issues তৈরি করুন
- Error message সহ বিস্তারিত লিখুন
- Environment details দিন

---

**Happy Coding! 🎉**
