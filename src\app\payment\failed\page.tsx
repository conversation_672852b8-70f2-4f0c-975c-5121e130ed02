'use client'

import { XCir<PERSON>, RefreshCw, ArrowLeft, Phone, Mail } from 'lucide-react'
import Link from 'next/link'

export default function PaymentFailed() {
  return (
    <div className="max-w-2xl mx-auto px-4 py-12">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <XCircle className="w-20 h-20 text-red-600 mx-auto mb-6" />
        
        <h1 className="text-3xl font-bold text-red-600 mb-4">
          পেমেন্ট ব্যর্থ হয়েছে
        </h1>
        
        <p className="text-gray-600 mb-8">
          দুঃখিত, আপনার পেমেন্ট সম্পন্ন হয়নি। অনুগ্রহ করে আবার চেষ্টা করুন।
        </p>

        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-red-800 mb-3">সম্ভাব্য কারণসমূহ:</h2>
          <ul className="text-left text-red-700 space-y-2">
            <li>• অপর্যাপ্ত ব্যালেন্স</li>
            <li>• নেটওয়ার্ক সংযোগের সমস্যা</li>
            <li>• কার্ড বা অ্যাকাউন্টের তথ্য ভুল</li>
            <li>• পেমেন্ট গেটওয়ের সাময়িক সমস্যা</li>
            <li>• লেনদেন সীমা অতিক্রম</li>
          </ul>
        </div>

        <div className="space-y-4 mb-8">
          <Link href="/payment" className="w-full btn-primary flex items-center justify-center">
            <RefreshCw className="w-5 h-5 mr-2" />
            আবার চেষ্টা করুন
          </Link>
          
          <div className="flex space-x-4">
            <Link href="/dashboard" className="flex-1 btn-secondary">
              ড্যাশবোর্ডে যান
            </Link>
            <Link href="/" className="flex-1 btn-secondary">
              হোম পেজ
            </Link>
          </div>
        </div>

        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold mb-4">সাহায্য প্রয়োজন?</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <Phone className="w-6 h-6 text-primary-600 mx-auto mb-2" />
              <p className="font-medium">ফোন করুন</p>
              <p className="text-sm text-gray-600">+৮৮০১৭xxxxxxxx</p>
              <p className="text-xs text-gray-500">সকাল ৯টা - রাত ৯টা</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <Mail className="w-6 h-6 text-primary-600 mx-auto mb-2" />
              <p className="font-medium">ইমেইল করুন</p>
              <p className="text-sm text-gray-600"><EMAIL></p>
              <p className="text-xs text-gray-500">২ৄ ঘন্টার মধ্যে উত্তর</p>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Link href="/" className="inline-flex items-center text-primary-600 hover:text-primary-700">
            <ArrowLeft className="w-4 h-4 mr-1" />
            হোম পেজে ফিরে যান
          </Link>
        </div>
      </div>
    </div>
  )
}
