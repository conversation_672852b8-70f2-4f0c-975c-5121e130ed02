'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, X, FileText, Image } from 'lucide-react'

interface FileUploadProps {
  onFileSelect: (file: File) => void
  accept?: string
  maxSize?: number
  label: string
  description?: string
  preview?: string | null
  onRemove?: () => void
}

export default function FileUpload({
  onFileSelect,
  accept = 'image/*',
  maxSize = 5 * 1024 * 1024, // 5MB
  label,
  description,
  preview,
  onRemove
}: FileUploadProps) {
  const [error, setError] = useState<string | null>(null)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setError(null)
    
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0]
      if (rejection.errors[0]?.code === 'file-too-large') {
        setError(`ফাইল সাইজ ${Math.round(maxSize / 1024 / 1024)}MB এর চেয়ে ছোট হতে হবে`)
      } else if (rejection.errors[0]?.code === 'file-invalid-type') {
        setError('অনুমোদিত ফাইল ফরম্যাট নয়')
      } else {
        setError('ফাইল আপলোডে সমস্যা হয়েছে')
      }
      return
    }

    if (acceptedFiles.length > 0) {
      onFileSelect(acceptedFiles[0])
    }
  }, [onFileSelect, maxSize])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept.split(',').reduce((acc, type) => {
      acc[type.trim()] = []
      return acc
    }, {} as Record<string, string[]>),
    maxSize,
    multiple: false
  })

  const isImage = accept.includes('image')
  const isPdf = accept.includes('pdf')

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      
      {preview ? (
        <div className="relative">
          {isImage ? (
            <div className="relative inline-block">
              <img 
                src={preview} 
                alt="Preview" 
                className="w-32 h-32 object-cover rounded-lg border"
              />
              {onRemove && (
                <button
                  type="button"
                  onClick={onRemove}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50">
              <FileText className="w-8 h-8 text-gray-600" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">ফাইল আপলোড হয়েছে</p>
                <p className="text-xs text-gray-500">ক্লিক করে পরিবর্তন করুন</p>
              </div>
              {onRemove && (
                <button
                  type="button"
                  onClick={onRemove}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
          )}
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive 
              ? 'border-primary-500 bg-primary-50' 
              : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
          }`}
        >
          <input {...getInputProps()} />
          
          <div className="space-y-2">
            {isImage ? (
              <Image className="w-12 h-12 text-gray-400 mx-auto" />
            ) : (
              <Upload className="w-12 h-12 text-gray-400 mx-auto" />
            )}
            
            <div>
              <p className="text-sm font-medium text-gray-900">
                {isDragActive ? 'ফাইল ছেড়ে দিন' : 'ফাইল আপলোড করুন'}
              </p>
              <p className="text-xs text-gray-500">
                {description || `ক্লিক করুন বা ড্র্যাগ করে ফাইল আনুন`}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                সর্বোচ্চ সাইজ: {Math.round(maxSize / 1024 / 1024)}MB
              </p>
            </div>
          </div>
        </div>
      )}
      
      {error && (
        <p className="text-red-500 text-sm">{error}</p>
      )}
    </div>
  )
}
