<?php
require_once 'config/database.php';

// Check if database connection exists
if (!$db) {
    echo "<div style='text-align: center; padding: 50px;'>";
    echo "<h2>ডেটাবেস কানেকশন সমস্যা</h2>";
    echo "<p>অনুগ্রহ করে প্রথমে ডেটাবেস সেটআপ করুন:</p>";
    echo "<a href='setup.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ডেটাবেস সেটআপ করুন</a>";
    echo "</div>";
    exit;
}

// Check if user is logged in (for now, we'll use a demo user)
$member_id = 1; // Demo user ID

try {
    // Get member information
    $member_query = "SELECT * FROM members WHERE id = ? AND status = 'approved'";
    $member_stmt = $db->prepare($member_query);
    $member_stmt->execute([$member_id]);
    $member = $member_stmt->fetch();

    if (!$member) {
        // If no member found, create demo data
        $member = [
            'id' => 1,
            'full_name' => 'মোহাম্মদ রহিম',
            'email' => '<EMAIL>',
            'phone' => '০১৭১২৩৪৫৬৭৮',
            'monthly_amount' => 2000,
            'created_at' => '2024-01-15'
        ];
    }

    // Get member statistics
    $stats_query = "
        SELECT 
            COALESCE(SUM(CASE WHEN type = 'deposit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_deposits,
            COALESCE(SUM(CASE WHEN type = 'profit' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_profits,
            COALESCE(SUM(CASE WHEN type = 'withdrawal' AND status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawals,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as total_transactions
        FROM transactions 
        WHERE member_id = ?
    ";
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->execute([$member_id]);
    $stats = $stats_stmt->fetch();

    // Calculate current balance
    $current_balance = $stats['total_deposits'] + $stats['total_profits'] - $stats['total_withdrawals'];

    // Get recent transactions
    $transactions_query = "
        SELECT * FROM transactions 
        WHERE member_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ";
    $transactions_stmt = $db->prepare($transactions_query);
    $transactions_stmt->execute([$member_id]);
    $transactions = $transactions_stmt->fetchAll();

    // If no transactions, create demo data
    if (empty($transactions)) {
        $transactions = [
            [
                'id' => 1,
                'amount' => 2000,
                'type' => 'deposit',
                'description' => 'মাসিক জমা - ডিসেম্বর ২০২৪',
                'status' => 'completed',
                'payment_method' => 'bkash',
                'created_at' => '2024-12-01 10:00:00'
            ],
            [
                'id' => 2,
                'amount' => 500,
                'type' => 'profit',
                'description' => 'মাসিক লাভ বণ্টন',
                'status' => 'completed',
                'payment_method' => null,
                'created_at' => '2024-11-25 15:00:00'
            ],
            [
                'id' => 3,
                'amount' => 2000,
                'type' => 'deposit',
                'description' => 'মাসিক জমা - নভেম্বর ২০২৪',
                'status' => 'completed',
                'payment_method' => 'nagad',
                'created_at' => '2024-11-01 10:00:00'
            ]
        ];
        
        // Update stats for demo
        $stats = [
            'total_deposits' => 24000,
            'total_profits' => 2500,
            'total_withdrawals' => 0,
            'total_transactions' => 12
        ];
        $current_balance = 26500;
    }

} catch (Exception $e) {
    $error_message = "ডেটা লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্যাশবোর্ড - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .progress-bar {
            background: linear-gradient(90deg, #10b981, #34d399);
            border-radius: 10px;
            height: 8px;
        }
        .notification-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users text-2xl text-blue-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">বন্ধুদের সংস্থা</h1>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="index.php" class="text-gray-600 hover:text-blue-600 transition-colors">হোম</a>
                    <a href="dashboard.php" class="text-blue-600 font-medium">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="text-gray-600 hover:text-blue-600 transition-colors">পেমেন্ট</a>
                    <div class="relative">
                        <button class="flex items-center text-gray-600 hover:text-blue-600">
                            <i class="fas fa-bell mr-1"></i>
                            <span class="notification-dot absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                        </button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <img src="https://via.placeholder.com/32x32/3b82f6/ffffff?text=R" alt="Profile" class="w-8 h-8 rounded-full">
                        <span class="text-gray-700 font-medium"><?php echo htmlspecialchars($member['full_name']); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="gradient-bg text-white py-8">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">স্বাগতম, <?php echo htmlspecialchars($member['full_name']); ?>!</h1>
                    <p class="text-blue-100">আপনার আর্থিক যাত্রার সারসংক্ষেপ দেখুন</p>
                </div>
                <div class="hidden md:block">
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold">৳<?php echo number_format($current_balance); ?></div>
                        <div class="text-sm text-blue-100">বর্তমান ব্যালেন্স</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Statistics Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">মোট জমা</p>
                        <p class="text-2xl font-bold text-gray-900">৳<?php echo number_format($stats['total_deposits']); ?></p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+৮.২%
                        </p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-piggy-bank text-2xl text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">মোট লাভ</p>
                        <p class="text-2xl font-bold text-green-600">৳<?php echo number_format($stats['total_profits']); ?></p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+১২.৫%
                        </p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-chart-line text-2xl text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">মাসিক জমা</p>
                        <p class="text-2xl font-bold text-gray-900">৳<?php echo number_format($member['monthly_amount']); ?></p>
                        <p class="text-xs text-gray-500 mt-1">নিয়মিত</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-calendar-alt text-2xl text-purple-600"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">মোট লেনদেন</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['total_transactions']; ?>টি</p>
                        <p class="text-xs text-blue-600 mt-1">সফল</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-exchange-alt text-2xl text-yellow-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Profile Card -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl card-shadow p-6 mb-6">
                    <div class="text-center mb-6">
                        <img src="https://via.placeholder.com/100x100/3b82f6/ffffff?text=<?php echo substr($member['full_name'], 0, 1); ?>" 
                             alt="Profile" class="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-blue-100">
                        <h2 class="text-xl font-bold text-gray-900"><?php echo htmlspecialchars($member['full_name']); ?></h2>
                        <p class="text-gray-600">সক্রিয় সদস্য</p>
                        <div class="mt-4 inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            অনুমোদিত
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">ইমেইল</span>
                            <span class="font-medium"><?php echo htmlspecialchars($member['email']); ?></span>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">মোবাইল</span>
                            <span class="font-medium"><?php echo htmlspecialchars($member['phone']); ?></span>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">সদস্য হওয়ার তারিখ</span>
                            <span class="font-medium"><?php echo format_date_bangla($member['created_at']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold mb-4">দ্রুত কাজ</h3>
                    <div class="space-y-3">
                        <a href="payment.php" class="w-full btn-primary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-credit-card mr-2"></i>
                            মাসিক পেমেন্ট করুন
                        </a>

                        <a href="setup_live_payment.php" class="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white flex items-center justify-center py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                            <i class="fas fa-rocket mr-2"></i>
                            Live Payment Setup
                        </a>
                        <button class="w-full btn-secondary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-download mr-2"></i>
                            স্টেটমেন্ট ডাউনলোড
                        </button>
                        <button class="w-full btn-secondary flex items-center justify-center py-3 rounded-lg">
                            <i class="fas fa-headset mr-2"></i>
                            সাহায্য ও সহায়তা
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Monthly Progress -->
                <div class="bg-white rounded-xl card-shadow p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">এই মাসের অগ্রগতি</h3>
                        <span class="text-sm text-gray-500">ডিসেম্বর ২০২ৄ</span>
                    </div>
                    <div class="mb-4">
                        <div class="flex justify-between text-sm mb-2">
                            <span>মাসিক লক্ষ্য</span>
                            <span>৳২,০০০ এর মধ্যে ৳২,০০০</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="progress-bar w-full h-2"></div>
                        </div>
                        <p class="text-xs text-green-600 mt-2">
                            <i class="fas fa-check-circle mr-1"></i>
                            এই মাসের লক্ষ্য সম্পূর্ণ!
                        </p>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold">সাম্প্রতিক লেনদেন</h3>
                        <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">সব দেখুন</a>
                    </div>
                    
                    <div class="space-y-4">
                        <?php foreach ($transactions as $transaction): ?>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center mr-4
                                    <?php echo $transaction['type'] == 'deposit' ? 'bg-blue-100 text-blue-600' : 
                                              ($transaction['type'] == 'profit' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'); ?>">
                                    <i class="fas <?php echo $transaction['type'] == 'deposit' ? 'fa-arrow-down' : 
                                                           ($transaction['type'] == 'profit' ? 'fa-arrow-up' : 'fa-arrow-up'); ?>"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900"><?php echo htmlspecialchars($transaction['description']); ?></p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo format_date_bangla($transaction['created_at']); ?>
                                        <?php if ($transaction['payment_method']): ?>
                                            • <?php echo strtoupper($transaction['payment_method']); ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold <?php echo $transaction['type'] == 'profit' ? 'text-green-600' : 'text-gray-900'; ?>">
                                    <?php echo $transaction['type'] == 'withdrawal' ? '-' : '+'; ?>৳<?php echo number_format($transaction['amount']); ?>
                                </p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs
                                    <?php echo $transaction['status'] == 'completed' ? 'bg-green-100 text-green-800' : 
                                              ($transaction['status'] == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                    <?php echo $transaction['status'] == 'completed' ? 'সম্পন্ন' : 
                                              ($transaction['status'] == 'pending' ? 'অপেক্ষমাণ' : 'ব্যর্থ'); ?>
                                </span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investment Overview -->
        <div class="mt-8 grid md:grid-cols-2 gap-6">
            <div class="bg-white rounded-xl card-shadow p-6">
                <h3 class="text-lg font-semibold mb-4">বিনিয়োগের সংক্ষিপ্ত বিবরণ</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <div>
                            <p class="font-medium text-blue-900">চলমান প্রকল্প</p>
                            <p class="text-sm text-blue-700">মোট বিনিয়োগ: ৳৫,০০,০০০</p>
                        </div>
                        <div class="text-2xl font-bold text-blue-600">৩টি</div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <div>
                            <p class="font-medium text-green-900">গড় মাসিক রিটার্ন</p>
                            <p class="text-sm text-green-700">গত ৬ মাসের গড়</p>
                        </div>
                        <div class="text-2xl font-bold text-green-600">১২.৫%</div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <h3 class="text-lg font-semibold mb-4">আসন্ন ইভেন্ট</h3>
                <div class="space-y-3">
                    <div class="flex items-center p-3 border-l-4 border-blue-500 bg-blue-50">
                        <i class="fas fa-calendar-check text-blue-600 mr-3"></i>
                        <div>
                            <p class="font-medium">মাসিক সভা</p>
                            <p class="text-sm text-gray-600">২৫ ডিসেম্বর, ২০২৪</p>
                        </div>
                    </div>
                    <div class="flex items-center p-3 border-l-4 border-green-500 bg-green-50">
                        <i class="fas fa-coins text-green-600 mr-3"></i>
                        <div>
                            <p class="font-medium">লাভ বণ্টন</p>
                            <p class="text-sm text-gray-600">৩০ ডিসেম্বর, ২০২৪</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Management -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-cogs mr-2 text-purple-600"></i>
                    পেমেন্ট ম্যানেজমেন্ট
                </h3>
                <div class="space-y-3">
                    <a href="setup_live_payment.php" class="flex items-center p-3 border border-green-200 rounded-lg hover:bg-green-50 transition-colors group">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200">
                            <i class="fas fa-rocket text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Live Payment Setup</p>
                            <p class="text-sm text-gray-600">রিয়েল পেমেন্ট গেটওয়ে কনফিগার করুন</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 ml-auto group-hover:text-green-600"></i>
                    </a>

                    <a href="payment_debug.php" class="flex items-center p-3 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors group">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200">
                            <i class="fas fa-bug text-blue-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Payment Debug</p>
                            <p class="text-sm text-gray-600">পেমেন্ট সমস্যা নির্ণয় ও সমাধান</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 ml-auto group-hover:text-blue-600"></i>
                    </a>

                    <a href="simple_ssl_setup.php" class="flex items-center p-3 border border-purple-200 rounded-lg hover:bg-purple-50 transition-colors group">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200">
                            <i class="fas fa-shield-alt text-purple-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">SSL Certificate</p>
                            <p class="text-sm text-gray-600">HTTPS সিকিউরিটি সেটআপ</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 ml-auto group-hover:text-purple-600"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Animate numbers on page load
            const numbers = document.querySelectorAll('.stat-card .text-2xl');
            numbers.forEach(number => {
                const finalValue = parseInt(number.textContent.replace(/[^\d]/g, ''));
                let currentValue = 0;
                const increment = finalValue / 50;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    number.textContent = number.textContent.replace(/[\d,]+/, Math.floor(currentValue).toLocaleString());
                }, 20);
            });

            // Add hover effects to transaction items
            const transactionItems = document.querySelectorAll('.space-y-4 > div');
            transactionItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                });
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
        });
    </script>
</body>
</html>
