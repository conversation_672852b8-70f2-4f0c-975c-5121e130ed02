'use client'

import { useState, useEffect } from 'react'
import { Users, DollarSign, TrendingUp, FileText, CheckCircle, XCircle, Clock } from 'lucide-react'

interface AdminStats {
  totalMembers: number
  pendingMembers: number
  totalDeposits: number
  monthlyDeposits: number
  activeProjects: number
  totalProfit: number
}

interface PendingMember {
  id: string
  fullName: string
  email: string
  phone: string
  monthlyAmount: number
  createdAt: string
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [pendingMembers, setPendingMembers] = useState<PendingMember[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAdminData()
  }, [])

  const loadAdminData = async () => {
    try {
      // Simulate loading admin data
      setTimeout(() => {
        setStats({
          totalMembers: 45,
          pendingMembers: 8,
          totalDeposits: 2500000,
          monthlyDeposits: 90000,
          activeProjects: 3,
          totalProfit: 125000
        })

        setPendingMembers([
          {
            id: '1',
            fullName: 'আহমেদ হাসান',
            email: '<EMAIL>',
            phone: '০১৭১২৩৪৫৬৭৮',
            monthlyAmount: 2000,
            createdAt: '২০২৪-১২-০১'
          },
          {
            id: '2',
            fullName: 'ফাতেমা খাতুন',
            email: '<EMAIL>',
            phone: '০১৮১২৩৪৫৬৭৮',
            monthlyAmount: 1500,
            createdAt: '২০২৪-১২-০২'
          },
          {
            id: '3',
            fullName: 'মোহাম্মদ করিম',
            email: '<EMAIL>',
            phone: '০১৯১২৩৪৫৬৭৮',
            monthlyAmount: 3000,
            createdAt: '২০২৪-১২-০৩'
          }
        ])

        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error loading admin data:', error)
      setLoading(false)
    }
  }

  const approveMember = async (memberId: string) => {
    try {
      // Here we would call API to approve member
      console.log('Approving member:', memberId)
      
      // Remove from pending list
      setPendingMembers(prev => prev.filter(m => m.id !== memberId))
      
      // Update stats
      if (stats) {
        setStats({
          ...stats,
          totalMembers: stats.totalMembers + 1,
          pendingMembers: stats.pendingMembers - 1
        })
      }
      
      alert('সদস্য অনুমোদিত হয়েছে!')
    } catch (error) {
      alert('অনুমোদনে সমস্যা হয়েছে!')
    }
  }

  const rejectMember = async (memberId: string) => {
    try {
      // Here we would call API to reject member
      console.log('Rejecting member:', memberId)
      
      // Remove from pending list
      setPendingMembers(prev => prev.filter(m => m.id !== memberId))
      
      // Update stats
      if (stats) {
        setStats({
          ...stats,
          pendingMembers: stats.pendingMembers - 1
        })
      }
      
      alert('সদস্যের আবেদন প্রত্যাখ্যান করা হয়েছে!')
    } catch (error) {
      alert('প্রত্যাখ্যানে সমস্যা হয়েছে!')
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">অ্যাডমিন ড্যাশবোর্ড</h1>
        <p className="text-gray-600">সংস্থার সামগ্রিক তথ্য ও ব্যবস্থাপনা</p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">মোট সদস্য</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalMembers}</p>
              </div>
              <Users className="w-10 h-10 text-blue-600" />
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">অপেক্ষমাণ সদস্য</p>
                <p className="text-3xl font-bold text-orange-600">{stats.pendingMembers}</p>
              </div>
              <Clock className="w-10 h-10 text-orange-600" />
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">মোট জমা</p>
                <p className="text-3xl font-bold text-green-600">৳{stats.totalDeposits.toLocaleString()}</p>
              </div>
              <DollarSign className="w-10 h-10 text-green-600" />
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">এই মাসের জমা</p>
                <p className="text-3xl font-bold text-blue-600">৳{stats.monthlyDeposits.toLocaleString()}</p>
              </div>
              <TrendingUp className="w-10 h-10 text-blue-600" />
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">চলমান প্রকল্প</p>
                <p className="text-3xl font-bold text-purple-600">{stats.activeProjects}</p>
              </div>
              <FileText className="w-10 h-10 text-purple-600" />
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">মোট লাভ</p>
                <p className="text-3xl font-bold text-green-600">৳{stats.totalProfit.toLocaleString()}</p>
              </div>
              <TrendingUp className="w-10 h-10 text-green-600" />
            </div>
          </div>
        </div>
      )}

      {/* Pending Members */}
      <div className="card">
        <h2 className="text-xl font-semibold mb-6">অপেক্ষমাণ সদস্যদের অনুমোদন</h2>
        
        {pendingMembers.length === 0 ? (
          <div className="text-center py-8">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">কোনো অপেক্ষমাণ সদস্য নেই</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3">নাম</th>
                  <th className="text-left py-3">ইমেইল</th>
                  <th className="text-left py-3">মোবাইল</th>
                  <th className="text-right py-3">মাসিক জমা</th>
                  <th className="text-left py-3">আবেদনের তারিখ</th>
                  <th className="text-center py-3">কার্যক্রম</th>
                </tr>
              </thead>
              <tbody>
                {pendingMembers.map((member) => (
                  <tr key={member.id} className="border-b hover:bg-gray-50">
                    <td className="py-4 font-medium">{member.fullName}</td>
                    <td className="py-4 text-gray-600">{member.email}</td>
                    <td className="py-4 text-gray-600">{member.phone}</td>
                    <td className="py-4 text-right font-medium">৳{member.monthlyAmount.toLocaleString()}</td>
                    <td className="py-4 text-gray-600">{member.createdAt}</td>
                    <td className="py-4">
                      <div className="flex justify-center space-x-2">
                        <button
                          onClick={() => approveMember(member.id)}
                          className="p-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors"
                          title="অনুমোদন করুন"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => rejectMember(member.id)}
                          className="p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
                          title="প্রত্যাখ্যান করুন"
                        >
                          <XCircle className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
        <button className="btn-primary">
          নতুন প্রকল্প যোগ করুন
        </button>
        <button className="btn-secondary">
          লাভ বণ্টন করুন
        </button>
        <button className="btn-secondary">
          রিপোর্ট তৈরি করুন
        </button>
        <button className="btn-secondary">
          সেটিংস
        </button>
      </div>
    </div>
  )
}
