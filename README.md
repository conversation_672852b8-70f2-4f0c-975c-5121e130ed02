# বন্ধুদের সংস্থা - Friends Organization

একটি আধুনিক ওয়েব অ্যাপ্লিকেশন যা বন্ধুদের নিয়ে গঠিত সংস্থার জন্য তৈরি। এখানে সদস্যরা মাসিক অর্থ জমা করে লাভজনক প্রকল্পে বিনিয়োগ করতে পারেন।

## ✨ বৈশিষ্ট্যসমূহ

- 🔐 **নিরাপদ সদস্য নিবন্ধন** - SSC মার্কশিট ও ছবি সহ
- 💳 **অনলাইন পেমেন্ট** - মোবাইল ব্যাংকিং, কার্ড ও ব্যাংক ট্রান্সফার
- 🏦 **সরাসরি ব্যাংক জমা** - নিরাপদ অর্থ ব্যবস্থাপনা
- 📊 **ব্যক্তিগত ড্যাশবোর্ড** - লেনদেন ও বিনিয়োগের তথ্য
- 📱 **রেসপন্সিভ ডিজাইন** - মোবাইল ও ডেস্কটপ উভয়ে কাজ করে
- 🔒 **ডেটা নিরাপত্তা** - Supabase দিয়ে সুরক্ষিত

## 🛠️ প্রযুক্তি স্ট্যাক

- **ফ্রন্টএন্ড**: Next.js 14, React, TypeScript, Tailwind CSS
- **ব্যাকএন্ড**: Supabase (PostgreSQL, Authentication, Storage)
- **পেমেন্ট**: SSLCommerz / Stripe
- **UI কম্পোনেন্ট**: Lucide React Icons
- **ফর্ম ম্যানেজমেন্ট**: React Hook Form

## 🚀 ইনস্টলেশন ও সেটআপ

### প্রয়োজনীয় সফটওয়্যার
- Node.js (18.0 বা তার পরের ভার্সন)
- npm বা yarn

### ধাপ ১: প্রকল্প ক্লোন করুন
```bash
git clone <repository-url>
cd friends-organization
```

### ধাপ ২: Dependencies ইনস্টল করুন
```bash
npm install
# অথবা
yarn install
```

### ধাপ ৩: Environment Variables সেটআপ করুন
```bash
cp .env.example .env.local
```

`.env.local` ফাইলে আপনার সঠিক তথ্য দিন:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Payment Gateway
SSLCOMMERZ_STORE_ID=your-store-id
SSLCOMMERZ_STORE_PASSWORD=your-store-password
```

### ধাপ ৪: Supabase ডেটাবেস সেটআপ

Supabase প্রকল্প তৈরি করুন এবং নিচের SQL স্ক্রিপ্ট চালান:

```sql
-- Members table
CREATE TABLE members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  full_name VARCHAR NOT NULL,
  email VARCHAR UNIQUE NOT NULL,
  phone VARCHAR NOT NULL,
  address TEXT NOT NULL,
  ssc_roll VARCHAR NOT NULL,
  ssc_year VARCHAR NOT NULL,
  ssc_board VARCHAR NOT NULL,
  monthly_amount INTEGER NOT NULL,
  photo_url VARCHAR,
  marksheet_url VARCHAR,
  status VARCHAR DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  member_id UUID REFERENCES members(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  type VARCHAR NOT NULL CHECK (type IN ('deposit', 'profit', 'withdrawal')),
  description TEXT,
  status VARCHAR DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  payment_method VARCHAR,
  transaction_id VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR NOT NULL,
  description TEXT,
  total_investment INTEGER NOT NULL,
  expected_return DECIMAL(5,2),
  start_date DATE NOT NULL,
  end_date DATE,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
('member-photos', 'member-photos', true),
('member-documents', 'member-documents', true);

-- RLS Policies
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Allow public read access to approved members
CREATE POLICY "Public members are viewable by everyone" ON members
  FOR SELECT USING (status = 'approved');

-- Allow users to insert their own member record
CREATE POLICY "Users can insert their own member record" ON members
  FOR INSERT WITH CHECK (true);

-- Allow users to view their own transactions
CREATE POLICY "Users can view their own transactions" ON transactions
  FOR SELECT USING (true);

-- Allow public read access to active projects
CREATE POLICY "Active projects are viewable by everyone" ON projects
  FOR SELECT USING (status = 'active');
```

### ধাপ ৫: ডেভেলপমেন্ট সার্ভার চালু করুন
```bash
npm run dev
# অথবা
yarn dev
```

ব্রাউজারে `http://localhost:3000` এ যান।

## 📁 প্রকল্প স্ট্রাকচার

```
src/
├── app/                    # Next.js App Router
│   ├── page.tsx           # হোম পেজ
│   ├── register/          # নিবন্ধন পেজ
│   ├── dashboard/         # ড্যাশবোর্ড
│   ├── payment/           # পেমেন্ট পেজ
│   ├── layout.tsx         # মূল লেআউট
│   └── globals.css        # গ্লোবাল স্টাইল
├── lib/
│   └── supabase.ts        # Supabase কনফিগারেশন
└── components/            # পুনঃব্যবহারযোগ্য কম্পোনেন্ট
```

## 🔧 কনফিগারেশন

### পেমেন্ট গেটওয়ে সেটআপ

#### SSLCommerz (বাংলাদেশের জন্য)
1. [SSLCommerz](https://www.sslcommerz.com/) এ অ্যাকাউন্ট তৈরি করুন
2. Store ID ও Password সংগ্রহ করুন
3. `.env.local` ফাইলে তথ্য যোগ করুন

#### Stripe (আন্তর্জাতিক)
1. [Stripe](https://stripe.com/) এ অ্যাকাউন্ট তৈরি করুন
2. API Keys সংগ্রহ করুন
3. `.env.local` ফাইলে তথ্য যোগ করুন

## 🚀 ডিপ্লয়মেন্ট

### Vercel এ ডিপ্লয় করুন
```bash
npm install -g vercel
vercel
```

### অন্যান্য প্ল্যাটফর্ম
- **Netlify**: `npm run build` এর পর `out` ফোল্ডার আপলোড করুন
- **Railway**: GitHub রিপোজিটরি কানেক্ট করুন
- **DigitalOcean**: Docker ব্যবহার করে ডিপ্লয় করুন

## 📝 ব্যবহারের নির্দেশনা

### সদস্য নিবন্ধন
1. `/register` পেজে যান
2. ব্যক্তিগত তথ্য পূরণ করুন
3. SSC মার্কশিট ও ছবি আপলোড করুন
4. নিবন্ধন সম্পন্ন করুন

### পেমেন্ট করা
1. `/payment` পেজে যান
2. পরিমাণ নির্বাচন করুন
3. পেমেন্ট পদ্ধতি বেছে নিন
4. পেমেন্ট সম্পন্ন করুন

### ড্যাশবোর্ড ব্যবহার
1. `/dashboard` পেজে যান
2. আপনার লেনদেনের ইতিহাস দেখুন
3. বিনিয়োগের তথ্য পর্যালোচনা করুন

## 🤝 অবদান রাখা

1. এই রিপোজিটরি Fork করুন
2. নতুন ব্রাঞ্চ তৈরি করুন (`git checkout -b feature/amazing-feature`)
3. আপনার পরিবর্তন Commit করুন (`git commit -m 'Add amazing feature'`)
4. ব্রাঞ্চে Push করুন (`git push origin feature/amazing-feature`)
5. Pull Request তৈরি করুন

## 📄 লাইসেন্স

এই প্রকল্পটি MIT লাইসেন্সের অধীনে লাইসেন্সপ্রাপ্ত।

## 📞 সাহায্য ও সহায়তা

কোনো সমস্যা বা প্রশ্ন থাকলে:
- GitHub Issues তৈরি করুন
- ইমেইল করুন: <EMAIL>
- ফোন: +৮৮০১৭xxxxxxxx

---

**বন্ধুদের সংস্থা** - একসাথে আমরা আরও শক্তিশালী! 💪
