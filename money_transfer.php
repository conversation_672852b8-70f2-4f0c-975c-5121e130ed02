<?php
session_start();
require_once 'config/database.php';
require_once 'config/bkash_config.php';

// Check if user is logged in
if (!isset($_SESSION['member_id'])) {
    header('Location: login.php');
    exit();
}

$member_id = $_SESSION['member_id'];
$error_message = '';
$success_message = '';

// Handle money transfer
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['transfer_money'])) {
    try {
        $recipient_number = formatBkashNumber($_POST['recipient_number']);
        $amount = floatval($_POST['amount']);
        $description = trim($_POST['description']);
        $save_recipient = isset($_POST['save_recipient']);
        $recipient_name = trim($_POST['recipient_name']);
        
        // Validation
        if (empty($recipient_number) || empty($amount)) {
            throw new Exception('সব ফিল্ড পূরণ করুন');
        }
        
        if (!validateBkashAmount($amount, 'send_money')) {
            throw new Exception('পরিমাণ ১০ টাকা থেকে ২৫,০০০ টাকার মধ্যে হতে হবে');
        }
        
        if ($recipient_number == formatBkashNumber(BKASH_ACCOUNT_NUMBER)) {
            throw new Exception('নিজের নম্বরে টাকা পাঠাতে পারবেন না');
        }
        
        // Calculate fee
        $fee = calculateBkashFee($amount, 'send_money');
        $total_amount = $amount + $fee;
        
        // Generate reference
        $reference = generateBkashReference();
        
        // For demo purposes, we'll simulate the transfer
        // In real implementation, you would call bKash API here
        $transfer_result = [
            'success' => true,
            'message' => 'Transfer initiated successfully',
            'reference' => $reference,
            'bkash_transaction_id' => 'BKT' . time() . rand(1000, 9999)
        ];
        
        if ($transfer_result['success']) {
            // Log the transaction
            logBkashTransaction([
                'reference_id' => $reference,
                'transaction_type' => 'send_money',
                'amount' => $amount,
                'fee' => $fee,
                'sender_number' => BKASH_ACCOUNT_NUMBER,
                'receiver_number' => $recipient_number,
                'status' => 'completed',
                'bkash_transaction_id' => $transfer_result['bkash_transaction_id']
            ]);
            
            // Save recipient if requested
            if ($save_recipient && !empty($recipient_name)) {
                $save_query = "INSERT IGNORE INTO transfer_recipients (member_id, recipient_name, recipient_number, recipient_type) VALUES (?, ?, ?, 'bkash')";
                $stmt = $db->prepare($save_query);
                $stmt->execute([$member_id, $recipient_name, $recipient_number]);
            }
            
            $success_message = "টাকা সফলভাবে পাঠানো হয়েছে! রেফারেন্স: " . $reference;
        } else {
            throw new Exception($transfer_result['message']);
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get saved recipients
$recipients_query = "SELECT * FROM transfer_recipients WHERE member_id = ? ORDER BY created_at DESC LIMIT 10";
$stmt = $db->prepare($recipients_query);
$stmt->execute([$member_id]);
$saved_recipients = $stmt->fetchAll();

// Get recent transfers
$transfers_query = "SELECT * FROM bkash_transactions WHERE sender_number = ? ORDER BY created_at DESC LIMIT 10";
$stmt = $db->prepare($transfers_query);
$stmt->execute([BKASH_ACCOUNT_NUMBER]);
$recent_transfers = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money Transfer - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users text-2xl text-orange-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">বন্ধুদের সংস্থা</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="dashboard.php" class="text-gray-600 hover:text-orange-600">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="text-gray-600 hover:text-orange-600">পেমেন্ট</a>
                    <a href="money_transfer.php" class="text-orange-600 font-medium">Money Transfer</a>
                    <a href="logout.php" class="text-gray-600 hover:text-orange-600">লগআউট</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-paper-plane mr-3 text-pink-600"></i>
                Money Transfer
            </h1>
            <p class="text-gray-600">আপনার bKash অ্যাকাউন্ট (<?php echo BKASH_ACCOUNT_NUMBER; ?>) থেকে টাকা পাঠান</p>
        </div>

        <?php if ($error_message): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Transfer Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-money-bill-transfer mr-2 text-blue-600"></i>
                        টাকা পাঠান
                    </h2>

                    <form method="POST" class="space-y-6">
                        <!-- Recipient Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                প্রাপকের bKash নম্বর *
                            </label>
                            <input type="text" name="recipient_number" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="01XXXXXXXXX" required>
                        </div>

                        <!-- Recipient Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                প্রাপকের নাম (ঐচ্ছিক)
                            </label>
                            <input type="text" name="recipient_name" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="প্রাপকের নাম">
                        </div>

                        <!-- Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                পরিমাণ (টাকা) *
                            </label>
                            <input type="number" name="amount" min="10" max="25000" step="0.01"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="১০০" required id="amount">
                            <div class="mt-2 text-sm text-gray-600">
                                <span>ফি: </span><span id="fee-display">০ টাকা</span> |
                                <span>মোট: </span><span id="total-display">০ টাকা</span>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                বিবরণ (ঐচ্ছিক)
                            </label>
                            <textarea name="description" rows="3"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="টাকা পাঠানোর কারণ..."></textarea>
                        </div>

                        <!-- Save Recipient -->
                        <div class="flex items-center">
                            <input type="checkbox" name="save_recipient" id="save_recipient" 
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="save_recipient" class="ml-2 text-sm text-gray-700">
                                এই প্রাপককে সংরক্ষণ করুন
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" name="transfer_money" 
                                class="w-full bg-gradient-to-r from-pink-600 to-pink-700 hover:from-pink-700 hover:to-pink-800 text-white font-bold py-4 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                            <i class="fas fa-paper-plane mr-2"></i>
                            টাকা পাঠান
                        </button>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Account Info -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-wallet mr-2 text-green-600"></i>
                        আপনার অ্যাকাউন্ট
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">bKash নম্বর:</span>
                            <span class="font-medium"><?php echo BKASH_ACCOUNT_NUMBER; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">দৈনিক সীমা:</span>
                            <span class="font-medium">৳<?php echo number_format(BKASH_DAILY_LIMIT); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">মাসিক সীমা:</span>
                            <span class="font-medium">৳<?php echo number_format(BKASH_MONTHLY_LIMIT); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Saved Recipients -->
                <?php if (!empty($saved_recipients)): ?>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-address-book mr-2 text-blue-600"></i>
                        সংরক্ষিত প্রাপক
                    </h3>
                    <div class="space-y-2">
                        <?php foreach ($saved_recipients as $recipient): ?>
                            <div class="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer recipient-item"
                                 data-number="<?php echo $recipient['recipient_number']; ?>"
                                 data-name="<?php echo htmlspecialchars($recipient['recipient_name']); ?>">
                                <div>
                                    <p class="font-medium text-sm"><?php echo htmlspecialchars($recipient['recipient_name']); ?></p>
                                    <p class="text-xs text-gray-600"><?php echo $recipient['recipient_number']; ?></p>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Transfers -->
        <?php if (!empty($recent_transfers)): ?>
        <div class="mt-8">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-history mr-2 text-purple-600"></i>
                    সাম্প্রতিক ট্রান্সফার
                </h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">রেফারেন্স</th>
                                <th class="text-left py-2">প্রাপক</th>
                                <th class="text-left py-2">পরিমাণ</th>
                                <th class="text-left py-2">ফি</th>
                                <th class="text-left py-2">স্ট্যাটাস</th>
                                <th class="text-left py-2">তারিখ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transfers as $transfer): ?>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="py-2 font-mono text-xs"><?php echo $transfer['reference_id']; ?></td>
                                    <td class="py-2"><?php echo $transfer['receiver_number']; ?></td>
                                    <td class="py-2">৳<?php echo number_format($transfer['amount'], 2); ?></td>
                                    <td class="py-2">৳<?php echo number_format($transfer['fee'], 2); ?></td>
                                    <td class="py-2">
                                        <span class="px-2 py-1 text-xs rounded-full 
                                            <?php echo $transfer['status'] == 'completed' ? 'bg-green-100 text-green-800' : 
                                                     ($transfer['status'] == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                            <?php echo $transfer['status']; ?>
                                        </span>
                                    </td>
                                    <td class="py-2"><?php echo date('d/m/Y H:i', strtotime($transfer['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Calculate fee and total amount
        document.getElementById('amount').addEventListener('input', function() {
            const amount = parseFloat(this.value) || 0;
            const feePercentage = 1.85;
            const fixedFee = 5;
            
            const fee = (amount * feePercentage / 100) + fixedFee;
            const total = amount + fee;
            
            document.getElementById('fee-display').textContent = fee.toFixed(2) + ' টাকা';
            document.getElementById('total-display').textContent = total.toFixed(2) + ' টাকা';
        });

        // Handle saved recipient selection
        document.querySelectorAll('.recipient-item').forEach(item => {
            item.addEventListener('click', function() {
                const number = this.dataset.number;
                const name = this.dataset.name;
                
                document.querySelector('input[name="recipient_number"]').value = number;
                document.querySelector('input[name="recipient_name"]').value = name;
            });
        });
    </script>
</body>
</html>
