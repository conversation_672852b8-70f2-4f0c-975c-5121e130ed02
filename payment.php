<?php
require_once 'config/database.php';

// Check if database connection exists
if (!$db) {
    echo "<div style='text-align: center; padding: 50px;'>";
    echo "<h2>ডেটাবেস কানেকশন সমস্যা</h2>";
    echo "<p>অনুগ্রহ করে প্রথমে ডেটাবেস সেটআপ করুন:</p>";
    echo "<a href='setup.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ডেটাবেস সেটআপ করুন</a>";
    echo "</div>";
    exit;
}

$success_message = '';
$error_message = '';

// Demo member data (in real app, this would come from session)
$member = [
    'id' => 1,
    'full_name' => 'মোহাম্মদ রহিম',
    'email' => '<EMAIL>',
    'phone' => '০১৭১২৩৪৫৬৭৮',
    'monthly_amount' => 2000
];

// Handle payment submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $amount = (float)$_POST['amount'];
        $payment_method = sanitize_input($_POST['payment_method']);
        $description = sanitize_input($_POST['description']);
        
        // Validation
        if ($amount < 100) {
            throw new Exception('পেমেন্ট পরিমাণ কমপক্ষে ১০০ টাকা হতে হবে।');
        }
        
        if (empty($payment_method)) {
            throw new Exception('পেমেন্ট পদ্ধতি নির্বাচন করুন।');
        }
        
        // Generate transaction ID
        $transaction_id = 'TXN' . date('YmdHis') . rand(1000, 9999);
        
        // Insert transaction record
        $insert_query = "INSERT INTO transactions (member_id, amount, type, description, status, payment_method, transaction_id, created_at) VALUES (?, ?, 'deposit', ?, 'completed', ?, ?, NOW())";
        $stmt = $db->prepare($insert_query);
        $stmt->execute([$member['id'], $amount, $description, $payment_method, $transaction_id]);
        
        $success_message = "পেমেন্ট সফল হয়েছে! লেনদেন নম্বর: " . $transaction_id;
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get member's recent transactions
try {
    $transactions_query = "SELECT * FROM transactions WHERE member_id = ? ORDER BY created_at DESC LIMIT 5";
    $stmt = $db->prepare($transactions_query);
    $stmt->execute([$member['id']]);
    $recent_transactions = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_transactions = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .payment-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .payment-method {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .payment-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .payment-method.selected {
            border-color: #3b82f6;
            background: #eff6ff;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2);
        }
        .amount-btn {
            transition: all 0.2s ease;
        }
        .amount-btn:hover {
            transform: scale(1.05);
        }
        .amount-btn.selected {
            background: #3b82f6;
            color: white;
            transform: scale(1.05);
        }
        .secure-badge {
            animation: pulse 2s infinite;
        }
        .payment-icon {
            width: 60px;
            height: 40px;
            object-fit: contain;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-credit-card text-2xl text-green-600 mr-3"></i>
                        <h1 class="text-xl font-bold text-gray-800">পেমেন্ট সিস্টেম</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.php" class="text-gray-600 hover:text-green-600 transition-colors">হোম</a>
                    <a href="dashboard.php" class="text-gray-600 hover:text-green-600 transition-colors">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="text-green-600 font-medium">পেমেন্ট</a>
                    <div class="flex items-center space-x-2">
                        <img src="https://via.placeholder.com/32x32/10b981/ffffff?text=R" alt="Profile" class="w-8 h-8 rounded-full">
                        <span class="text-gray-700 font-medium"><?php echo htmlspecialchars($member['full_name']); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="payment-gradient text-white py-8">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center">
                <h1 class="text-3xl font-bold mb-2">নিরাপদ পেমেন্ট</h1>
                <p class="text-green-100">আপনার মাসিক জমা দিন সহজ ও নিরাপদ উপায়ে</p>
                <div class="mt-4 inline-flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2">
                    <i class="fas fa-shield-alt mr-2 secure-badge"></i>
                    <span class="text-sm">১০০% নিরাপদ ও এনক্রিপ্টেড</span>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-6xl mx-auto px-4 py-8">
        <?php if ($success_message): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6 flex items-center">
                <i class="fas fa-check-circle text-2xl mr-3"></i>
                <div>
                    <h4 class="font-bold">পেমেন্ট সফল!</h4>
                    <p><?php echo $success_message; ?></p>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6 flex items-center">
                <i class="fas fa-exclamation-circle text-2xl mr-3"></i>
                <div>
                    <h4 class="font-bold">পেমেন্ট ব্যর্থ!</h4>
                    <p><?php echo $error_message; ?></p>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Payment Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-money-bill-wave mr-3 text-green-600"></i>
                        পেমেন্ট করুন
                    </h2>

                    <form method="POST" id="paymentForm">
                        <!-- Amount Selection -->
                        <div class="mb-8">
                            <label class="block text-lg font-semibold mb-4">পেমেন্ট পরিমাণ নির্বাচন করুন</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                <button type="button" class="amount-btn bg-gray-100 hover:bg-blue-100 border-2 border-gray-200 rounded-lg p-4 text-center font-medium" data-amount="1000">
                                    ৳১,০০০
                                </button>
                                <button type="button" class="amount-btn bg-gray-100 hover:bg-blue-100 border-2 border-gray-200 rounded-lg p-4 text-center font-medium selected" data-amount="2000">
                                    ৳২,০০০
                                    <div class="text-xs text-gray-500 mt-1">প্রস্তাবিত</div>
                                </button>
                                <button type="button" class="amount-btn bg-gray-100 hover:bg-blue-100 border-2 border-gray-200 rounded-lg p-4 text-center font-medium" data-amount="3000">
                                    ৳৩,০০০
                                </button>
                                <button type="button" class="amount-btn bg-gray-100 hover:bg-blue-100 border-2 border-gray-200 rounded-lg p-4 text-center font-medium" data-amount="5000">
                                    ৳৫,০০০
                                </button>
                            </div>
                            <div class="flex items-center">
                                <label class="text-gray-700 mr-3">অথবা কাস্টম পরিমাণ:</label>
                                <input type="number" name="amount" id="customAmount" class="form-control w-32" placeholder="পরিমাণ" min="100" value="2000" required>
                                <span class="ml-2 text-gray-600">টাকা</span>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="mb-8">
                            <label class="block text-lg font-semibold mb-4">পেমেন্ট পদ্ধতি নির্বাচন করুন</label>
                            <div class="grid md:grid-cols-2 gap-4">
                                <!-- Mobile Banking -->
                                <div class="space-y-3">
                                    <h3 class="font-medium text-gray-700 mb-3">মোবাইল ব্যাংকিং</h3>
                                    
                                    <div class="payment-method border-2 border-gray-200 rounded-lg p-4 flex items-center" data-method="bkash">
                                        <input type="radio" name="payment_method" value="bkash" id="bkash" class="mr-3">
                                        <img src="https://via.placeholder.com/60x40/e91e63/ffffff?text=bKash" alt="bKash" class="payment-icon mr-3">
                                        <div>
                                            <label for="bkash" class="font-medium cursor-pointer">bKash</label>
                                            <p class="text-sm text-gray-500">মোবাইল নম্বর দিয়ে পেমেন্ট</p>
                                        </div>
                                    </div>

                                    <div class="payment-method border-2 border-gray-200 rounded-lg p-4 flex items-center" data-method="nagad">
                                        <input type="radio" name="payment_method" value="nagad" id="nagad" class="mr-3">
                                        <img src="https://via.placeholder.com/60x40/ff9800/ffffff?text=Nagad" alt="Nagad" class="payment-icon mr-3">
                                        <div>
                                            <label for="nagad" class="font-medium cursor-pointer">Nagad</label>
                                            <p class="text-sm text-gray-500">ডিজিটাল ফিন্যান্সিয়াল সেবা</p>
                                        </div>
                                    </div>

                                    <div class="payment-method border-2 border-gray-200 rounded-lg p-4 flex items-center" data-method="rocket">
                                        <input type="radio" name="payment_method" value="rocket" id="rocket" class="mr-3">
                                        <img src="https://via.placeholder.com/60x40/9c27b0/ffffff?text=Rocket" alt="Rocket" class="payment-icon mr-3">
                                        <div>
                                            <label for="rocket" class="font-medium cursor-pointer">Rocket</label>
                                            <p class="text-sm text-gray-500">ডাচ-বাংলা ব্যাংক</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cards & Banking -->
                                <div class="space-y-3">
                                    <h3 class="font-medium text-gray-700 mb-3">কার্ড ও ব্যাংকিং</h3>
                                    
                                    <div class="payment-method border-2 border-gray-200 rounded-lg p-4 flex items-center" data-method="card">
                                        <input type="radio" name="payment_method" value="card" id="card" class="mr-3">
                                        <img src="https://via.placeholder.com/60x40/1976d2/ffffff?text=CARD" alt="Card" class="payment-icon mr-3">
                                        <div>
                                            <label for="card" class="font-medium cursor-pointer">ডেবিট/ক্রেডিট কার্ড</label>
                                            <p class="text-sm text-gray-500">Visa, Mastercard</p>
                                        </div>
                                    </div>

                                    <div class="payment-method border-2 border-gray-200 rounded-lg p-4 flex items-center" data-method="bank">
                                        <input type="radio" name="payment_method" value="bank" id="bank" class="mr-3">
                                        <img src="https://via.placeholder.com/60x40/4caf50/ffffff?text=BANK" alt="Bank" class="payment-icon mr-3">
                                        <div>
                                            <label for="bank" class="font-medium cursor-pointer">ব্যাংক ট্রান্সফার</label>
                                            <p class="text-sm text-gray-500">নেট ব্যাংকিং</p>
                                        </div>
                                    </div>

                                    <div class="payment-method border-2 border-gray-200 rounded-lg p-4 flex items-center" data-method="upay">
                                        <input type="radio" name="payment_method" value="upay" id="upay" class="mr-3">
                                        <img src="https://via.placeholder.com/60x40/00bcd4/ffffff?text=Upay" alt="Upay" class="payment-icon mr-3">
                                        <div>
                                            <label for="upay" class="font-medium cursor-pointer">Upay</label>
                                            <p class="text-sm text-gray-500">UCB ফিনটেক</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-6">
                            <label class="form-label">পেমেন্টের বিবরণ (ঐচ্ছিক)</label>
                            <select name="description" class="form-control">
                                <option value="মাসিক জমা - <?php echo date('F Y'); ?>">মাসিক জমা - <?php echo date('F Y'); ?></option>
                                <option value="অতিরিক্ত জমা">অতিরিক্ত জমা</option>
                                <option value="বিশেষ অনুদান">বিশেষ অনুদান</option>
                                <option value="প্রকল্প বিনিয়োগ">প্রকল্প বিনিয়োগ</option>
                            </select>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="w-full btn-primary text-xl py-4 rounded-lg flex items-center justify-center">
                            <i class="fas fa-lock mr-2"></i>
                            নিরাপদ পেমেন্ট করুন
                        </button>

                        <div class="mt-4 text-center text-sm text-gray-500">
                            <i class="fas fa-shield-alt mr-1"></i>
                            আপনার তথ্য SSL এনক্রিপশন দিয়ে সুরক্ষিত
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Payment Summary -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-receipt mr-2 text-blue-600"></i>
                        পেমেন্ট সারসংক্ষেপ
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">সদস্যের নাম:</span>
                            <span class="font-medium"><?php echo htmlspecialchars($member['full_name']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">পেমেন্ট পরিমাণ:</span>
                            <span class="font-bold text-green-600" id="summaryAmount">৳২,০০০</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">প্রসেসিং ফি:</span>
                            <span class="text-green-600">৳০ (ফ্রি)</span>
                        </div>
                        <hr>
                        <div class="flex justify-between text-lg font-bold">
                            <span>মোট পরিমাণ:</span>
                            <span class="text-green-600" id="totalAmount">৳২,০০০</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-history mr-2 text-purple-600"></i>
                        সাম্প্রতিক লেনদেন
                    </h3>
                    <div class="space-y-3">
                        <?php if (empty($recent_transactions)): ?>
                            <p class="text-gray-500 text-center py-4">কোনো লেনদেন নেই</p>
                        <?php else: ?>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                        <i class="fas fa-check text-green-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium"><?php echo htmlspecialchars($transaction['description']); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo date('d/m/Y', strtotime($transaction['created_at'])); ?></p>
                                    </div>
                                </div>
                                <span class="font-bold text-green-600">৳<?php echo number_format($transaction['amount']); ?></span>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Amount button selection
            const amountButtons = document.querySelectorAll('.amount-btn');
            const customAmountInput = document.getElementById('customAmount');
            const summaryAmount = document.getElementById('summaryAmount');
            const totalAmount = document.getElementById('totalAmount');

            amountButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    amountButtons.forEach(btn => btn.classList.remove('selected'));
                    this.classList.add('selected');
                    
                    const amount = this.dataset.amount;
                    customAmountInput.value = amount;
                    updateSummary(amount);
                });
            });

            customAmountInput.addEventListener('input', function() {
                amountButtons.forEach(btn => btn.classList.remove('selected'));
                updateSummary(this.value);
            });

            function updateSummary(amount) {
                const formattedAmount = '৳' + parseInt(amount).toLocaleString();
                summaryAmount.textContent = formattedAmount;
                totalAmount.textContent = formattedAmount;
            }

            // Payment method selection
            const paymentMethods = document.querySelectorAll('.payment-method');
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    paymentMethods.forEach(m => m.classList.remove('selected'));
                    this.classList.add('selected');
                    
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                });
            });

            // Form validation
            document.getElementById('paymentForm').addEventListener('submit', function(e) {
                const amount = parseInt(customAmountInput.value);
                const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
                
                if (amount < 100) {
                    e.preventDefault();
                    alert('পেমেন্ট পরিমাণ কমপক্ষে ১০০ টাকা হতে হবে!');
                    return;
                }
                
                if (!paymentMethod) {
                    e.preventDefault();
                    alert('পেমেন্ট পদ্ধতি নির্বাচন করুন!');
                    return;
                }
                
                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>প্রসেসিং...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html>
