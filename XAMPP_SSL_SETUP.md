# XAMPP এ SSL Certificate সেটআপ

## ধাপ ১: OpenSSL Check করুন

### Windows এ OpenSSL Install করুন:
1. **Download OpenSSL:**
   - যান: https://slproweb.com/products/Win32OpenSSL.html
   - Download: "Win64 OpenSSL v3.x.x Light"
   - Install করুন

2. **অথবা XAMPP এর OpenSSL ব্যবহার করুন:**
   ```
   Path: C:\xampp\apache\bin\openssl.exe
   ```

## ধাপ ২: SSL Certificate তৈরি করুন

### Command Prompt খুলুন (Administrator হিসেবে):
```cmd
cd C:\xampp\htdocs\1992
```

### Certificate তৈরি করুন:
```cmd
# Private key তৈরি করুন
C:\xampp\apache\bin\openssl.exe genrsa -out localhost.key 2048

# Certificate Signing Request তৈরি করুন
C:\xampp\apache\bin\openssl.exe req -new -key localhost.key -out localhost.csr

# Self-signed certificate তৈরি করুন
C:\xampp\apache\bin\openssl.exe x509 -req -days 365 -in localhost.csr -signkey localhost.key -out localhost.crt
```

### Certificate Information দিন:
```
Country Name: BD
State: Dhaka
City: Dhaka
Organization: Friends Organization
Organizational Unit: IT
Common Name: localhost
Email: admin@localhost
```

## ধাপ ৩: XAMPP Apache Configuration

### 1. httpd.conf Edit করুন:
```
File: C:\xampp\apache\conf\httpd.conf

# এই লাইনগুলো uncomment করুন:
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule ssl_module modules/mod_ssl.so
Include conf/extra/httpd-ssl.conf
```

### 2. httpd-ssl.conf Edit করুন:
```
File: C:\xampp\apache\conf\extra\httpd-ssl.conf

# Virtual Host section এ যোগ করুন:
<VirtualHost localhost:443>
    DocumentRoot "C:/xampp/htdocs/1992"
    ServerName localhost:443
    ServerAlias localhost
    
    SSLEngine on
    SSLCertificateFile "C:/xampp/htdocs/1992/localhost.crt"
    SSLCertificateKeyFile "C:/xampp/htdocs/1992/localhost.key"
    
    <Directory "C:/xampp/htdocs/1992">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### 3. httpd-vhosts.conf Edit করুন:
```
File: C:\xampp\apache\conf\extra\httpd-vhosts.conf

# HTTP Virtual Host
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/1992"
    ServerName localhost
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

# HTTPS Virtual Host
<VirtualHost *:443>
    DocumentRoot "C:/xampp/htdocs/1992"
    ServerName localhost
    
    SSLEngine on
    SSLCertificateFile "C:/xampp/htdocs/1992/localhost.crt"
    SSLCertificateKeyFile "C:/xampp/htdocs/1992/localhost.key"
    
    <Directory "C:/xampp/htdocs/1992">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

## ধাপ ৪: Windows এ Certificate Trust করুন

### 1. Certificate Manager খুলুন:
```
Windows + R → certmgr.msc → Enter
```

### 2. Certificate Import করুন:
```
1. Trusted Root Certification Authorities → Certificates
2. Right click → All Tasks → Import
3. Browse: localhost.crt file select করুন
4. Place in: Trusted Root Certification Authorities
5. Finish
```

### 3. Browser Restart করুন

## ধাপ ৫: Apache Restart করুন

### XAMPP Control Panel এ:
```
1. Apache Stop করুন
2. Apache Start করুন
3. Check: Port 443 listening
```

## ধাপ ৬: Test করুন

### Browser এ যান:
```
🔒 https://localhost/1992
✅ Green lock icon দেখতে পাবেন
🛡️ Certificate valid দেখাবে
```

## Troubleshooting

### সমস্যা ১: Apache Start হচ্ছে না
```
Solution:
- Check Apache error logs
- Verify certificate file paths
- Check port 443 conflicts
```

### সমস্যা ২: Certificate Error
```
Solution:
- Re-import certificate to Trusted Root
- Clear browser cache
- Check certificate validity
```

### সমস্যা ৩: Mixed Content Warning
```
Solution:
- Update all HTTP links to HTTPS
- Check .htaccess rules
- Verify SSL configuration
```

## Quick Commands

### Certificate তৈরি (One-liner):
```cmd
cd C:\xampp\htdocs\1992 && C:\xampp\apache\bin\openssl.exe req -x509 -newkey rsa:4096 -keyout localhost.key -out localhost.crt -days 365 -nodes -subj "/C=BD/ST=Dhaka/L=Dhaka/O=Friends Organization/CN=localhost"
```

### Apache Config Test:
```cmd
C:\xampp\apache\bin\httpd.exe -t
```

### SSL Test:
```cmd
C:\xampp\apache\bin\openssl.exe s_client -connect localhost:443
```
