<?php
/**
 * Automated SSL Setup for Localhost
 * Creates SSL certificate and configures XAMPP
 */

$setup_steps = [];
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $project_path = __DIR__;
        $xampp_path = 'C:\\xampp';
        
        // Step 1: Check for OpenSSL in multiple locations
        $openssl_paths = [
            $xampp_path . '\\apache\\bin\\openssl.exe',
            $xampp_path . '\\php\\openssl.exe',
            'C:\\Program Files\\OpenSSL-Win64\\bin\\openssl.exe',
            'C:\\OpenSSL-Win64\\bin\\openssl.exe',
            'openssl.exe' // System PATH
        ];

        $openssl_path = null;
        foreach ($openssl_paths as $path) {
            if (file_exists($path)) {
                $openssl_path = $path;
                break;
            }
        }

        // If not found, try system command
        if (!$openssl_path) {
            exec('where openssl', $output, $return_code);
            if ($return_code === 0 && !empty($output)) {
                $openssl_path = trim($output[0]);
            }
        }

        if (!$openssl_path) {
            // Create certificates using PHP instead
            $setup_steps[] = '⚠️ OpenSSL not found, using PHP alternative method';
            createSSLWithPHP($cert_dir);
        } else {
            $setup_steps[] = '✅ OpenSSL found at: ' . $openssl_path;
        }
        
        // Step 2: Create certificates directory
        $cert_dir = $project_path . '\\certificates';
        if (!is_dir($cert_dir)) {
            mkdir($cert_dir, 0755, true);
        }
        $setup_steps[] = '✅ Certificates directory created';
        
        // Step 3: Generate SSL certificate
        $key_file = $cert_dir . '\\localhost.key';
        $crt_file = $cert_dir . '\\localhost.crt';
        
        // Generate private key
        $key_cmd = "\"$openssl_path\" genrsa -out \"$key_file\" 2048";
        exec($key_cmd, $output, $return_code);
        
        if ($return_code !== 0) {
            throw new Exception('Failed to generate private key');
        }
        $setup_steps[] = '✅ Private key generated';
        
        // Generate self-signed certificate
        $cert_cmd = "\"$openssl_path\" req -new -x509 -key \"$key_file\" -out \"$crt_file\" -days 365 -subj \"/C=BD/ST=Dhaka/L=Dhaka/O=Friends Organization/CN=localhost\"";
        exec($cert_cmd, $output, $return_code);
        
        if ($return_code !== 0) {
            throw new Exception('Failed to generate certificate');
        }
        $setup_steps[] = '✅ SSL Certificate generated';
        
        // Step 4: Create Apache configuration
        $apache_config = generateApacheConfig($project_path, $key_file, $crt_file);
        $config_file = $project_path . '\\apache_ssl_config.txt';
        file_put_contents($config_file, $apache_config);
        $setup_steps[] = '✅ Apache configuration generated';
        
        // Step 5: Create .htaccess for HTTPS redirect
        $htaccess_content = "RewriteEngine On\nRewriteCond %{HTTPS} off\nRewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]\n";
        file_put_contents($project_path . '\\.htaccess', $htaccess_content);
        $setup_steps[] = '✅ .htaccess file created for HTTPS redirect';
        
        // Step 6: Generate Windows certificate import script
        $cert_import_script = generateCertImportScript($crt_file);
        $import_file = $project_path . '\\import_certificate.bat';
        file_put_contents($import_file, $cert_import_script);
        $setup_steps[] = '✅ Certificate import script created';
        
        $success = true;
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}

function generateApacheConfig($project_path, $key_file, $crt_file) {
    $config = "
# Add this to C:\\xampp\\apache\\conf\\extra\\httpd-ssl.conf

<VirtualHost localhost:443>
    DocumentRoot \"$project_path\"
    ServerName localhost:443
    ServerAlias localhost
    
    SSLEngine on
    SSLCertificateFile \"$crt_file\"
    SSLCertificateKeyFile \"$key_file\"
    
    <Directory \"$project_path\">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    # Security headers
    Header always set Strict-Transport-Security \"max-age=63072000; includeSubDomains\"
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
</VirtualHost>

# Also add this to C:\\xampp\\apache\\conf\\extra\\httpd-vhosts.conf

<VirtualHost *:80>
    DocumentRoot \"$project_path\"
    ServerName localhost
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>
";
    
    return $config;
}

function generateCertImportScript($crt_file) {
    return "@echo off
echo Importing SSL Certificate to Windows Trust Store...
echo.
certutil -addstore -f \"ROOT\" \"$crt_file\"
echo.
echo Certificate imported successfully!
echo Please restart your browser.
echo.
pause";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Localhost SSL Setup - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="gradient-bg text-white py-8">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h1 class="text-3xl font-bold mb-2">
                <i class="fas fa-shield-alt mr-3"></i>
                Localhost SSL Setup
            </h1>
            <p class="text-green-100">XAMPP এ HTTPS সেটআপ করুন Development এর জন্য</p>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 py-8">
        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    সমস্যা হয়েছে:
                </h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    SSL Certificate সফলভাবে তৈরি হয়েছে!
                </h4>
                <div class="space-y-2">
                    <?php foreach ($setup_steps as $step): ?>
                        <div><?php echo $step; ?></div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center text-green-600">
                    <i class="fas fa-list-ol mr-2"></i>
                    পরবর্তী ধাপসমূহ
                </h2>

                <div class="space-y-6">
                    <!-- Step 1 -->
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h3 class="text-lg font-semibold text-blue-600 mb-2">
                            ১. Apache Configuration আপডেট করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            apache_ssl_config.txt ফাইলের content copy করে XAMPP এর configuration ফাইলে paste করুন:
                        </p>
                        <div class="bg-gray-100 p-3 rounded text-sm">
                            <strong>File 1:</strong> C:\xampp\apache\conf\extra\httpd-ssl.conf<br>
                            <strong>File 2:</strong> C:\xampp\apache\conf\extra\httpd-vhosts.conf
                        </div>
                        <a href="apache_ssl_config.txt" target="_blank" class="inline-flex items-center mt-2 text-blue-600 hover:text-blue-700">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            Configuration ফাইল দেখুন
                        </a>
                    </div>

                    <!-- Step 2 -->
                    <div class="border-l-4 border-green-500 pl-4">
                        <h3 class="text-lg font-semibold text-green-600 mb-2">
                            ২. Certificate Windows এ Import করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Administrator হিসেবে import_certificate.bat ফাইল run করুন:
                        </p>
                        <button onclick="runImportScript()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                            <i class="fas fa-play mr-2"></i>
                            Certificate Import করুন
                        </button>
                    </div>

                    <!-- Step 3 -->
                    <div class="border-l-4 border-yellow-500 pl-4">
                        <h3 class="text-lg font-semibold text-yellow-600 mb-2">
                            ৩. XAMPP Apache Restart করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            XAMPP Control Panel এ Apache Stop করে আবার Start করুন।
                        </p>
                        <div class="bg-yellow-50 p-3 rounded text-sm">
                            <strong>Note:</strong> Port 443 (HTTPS) এবং Port 80 (HTTP) দুটোই চালু থাকতে হবে।
                        </div>
                    </div>

                    <!-- Step 4 -->
                    <div class="border-l-4 border-purple-500 pl-4">
                        <h3 class="text-lg font-semibold text-purple-600 mb-2">
                            ৪. Test করুন
                        </h3>
                        <p class="text-gray-700 mb-3">
                            Browser এ যান এবং HTTPS connection verify করুন:
                        </p>
                        <div class="space-y-2">
                            <a href="https://localhost/1992" target="_blank" class="inline-flex items-center text-purple-600 hover:text-purple-700">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                https://localhost/1992 (HTTPS)
                            </a>
                            <br>
                            <a href="http://localhost/1992" target="_blank" class="inline-flex items-center text-gray-600 hover:text-gray-700">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                http://localhost/1992 (HTTP - should redirect)
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-8 text-center">
                    <a href="setup_live_payment.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Live Payment Setup এ যান
                    </a>
                </div>
            </div>

        <?php else: ?>
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">
                    <i class="fas fa-cog mr-2 text-green-600"></i>
                    Localhost SSL Certificate Setup
                </h2>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        এই Setup কি করবে:
                    </h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Localhost এর জন্য SSL Certificate তৈরি করবে</li>
                        <li>• XAMPP Apache এর জন্য HTTPS configuration তৈরি করবে</li>
                        <li>• HTTP থেকে HTTPS এ auto redirect setup করবে</li>
                        <li>• Windows এ certificate trust করার script তৈরি করবে</li>
                    </ul>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-yellow-800 mb-2">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        প্রয়োজনীয় শর্ত:
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• XAMPP properly installed হতে হবে</li>
                        <li>• Apache running থাকতে হবে</li>
                        <li>• Administrator access প্রয়োজন</li>
                        <li>• OpenSSL XAMPP এ available থাকতে হবে</li>
                    </ul>
                </div>

                <form method="POST" class="text-center">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-8 rounded-lg text-lg">
                        <i class="fas fa-play mr-2"></i>
                        SSL Certificate Setup শুরু করুন
                    </button>
                </form>

                <div class="mt-6 text-center text-sm text-gray-500">
                    <p>
                        <i class="fas fa-clock mr-1"></i>
                        Setup time: প্রায় ২-৩ মিনিট
                    </p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Help Section -->
        <div class="mt-8 bg-gray-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">
                <i class="fas fa-question-circle mr-2 text-blue-600"></i>
                সাহায্য ও Troubleshooting
            </h3>
            <div class="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-medium mb-2">সাধারণ সমস্যা:</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• Apache start হচ্ছে না</li>
                        <li>• Certificate error দেখাচ্ছে</li>
                        <li>• HTTPS redirect কাজ করছে না</li>
                        <li>• Mixed content warning</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">সমাধান:</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• <a href="XAMPP_SSL_SETUP.md" class="text-blue-600 hover:underline">বিস্তারিত গাইড দেখুন</a></li>
                        <li>• XAMPP error logs চেক করুন</li>
                        <li>• Port conflicts চেক করুন</li>
                        <li>• Browser cache clear করুন</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function runImportScript() {
            alert('import_certificate.bat ফাইলে right-click করে "Run as administrator" select করুন।');
        }
    </script>
</body>
</html>
