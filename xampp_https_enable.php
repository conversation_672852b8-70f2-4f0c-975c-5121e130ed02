<?php
/**
 * <PERSON>AMPP HTTPS Enable - No OpenSSL Required
 * Uses XAMPP's built-in SSL certificate
 */

$success = false;
$errors = [];
$steps = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $xampp_path = 'C:\\xampp';
        $project_path = __DIR__;
        
        // Step 1: Check XAMPP installation
        if (!is_dir($xampp_path)) {
            throw new Exception('XAMPP not found at C:\\xampp. Please check installation path.');
        }
        $steps[] = '✅ XAMPP installation found';
        
        // Step 2: Check if SSL is already enabled in XAMPP
        $httpd_conf = $xampp_path . '\\apache\\conf\\httpd.conf';
        if (!file_exists($httpd_conf)) {
            throw new Exception('Apache configuration file not found');
        }
        
        $conf_content = file_get_contents($httpd_conf);
        
        // Step 3: Enable SSL module if not enabled
        if (strpos($conf_content, '#LoadModule ssl_module') !== false) {
            $conf_content = str_replace('#LoadModule ssl_module', 'LoadModule ssl_module', $conf_content);
            file_put_contents($httpd_conf, $conf_content);
            $steps[] = '✅ SSL module enabled in Apache';
        } else {
            $steps[] = '✅ SSL module already enabled';
        }
        
        // Step 4: Enable SSL configuration include
        if (strpos($conf_content, '#Include conf/extra/httpd-ssl.conf') !== false) {
            $conf_content = str_replace('#Include conf/extra/httpd-ssl.conf', 'Include conf/extra/httpd-ssl.conf', $conf_content);
            file_put_contents($httpd_conf, $conf_content);
            $steps[] = '✅ SSL configuration included';
        } else {
            $steps[] = '✅ SSL configuration already included';
        }
        
        // Step 5: Create virtual host for our project
        $vhost_conf = $xampp_path . '\\apache\\conf\\extra\\httpd-vhosts.conf';
        $vhost_content = file_get_contents($vhost_conf);
        
        $project_vhost = "
# Friends Organization HTTPS Virtual Host
<VirtualHost *:443>
    DocumentRoot \"$project_path\"
    ServerName localhost
    ServerAlias localhost
    
    SSLEngine on
    SSLCertificateFile \"conf/ssl.crt/server.crt\"
    SSLCertificateKeyFile \"conf/ssl.key/server.key\"
    
    <Directory \"$project_path\">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
</VirtualHost>

# HTTP to HTTPS redirect
<VirtualHost *:80>
    DocumentRoot \"$project_path\"
    ServerName localhost
    
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>
";
        
        // Check if our vhost already exists
        if (strpos($vhost_content, 'Friends Organization HTTPS') === false) {
            file_put_contents($vhost_conf, $vhost_content . $project_vhost);
            $steps[] = '✅ Virtual host configuration added';
        } else {
            $steps[] = '✅ Virtual host already configured';
        }
        
        // Step 6: Create .htaccess for HTTPS redirect
        $htaccess_content = "# HTTPS Redirect
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
    Header always set Referrer-Policy \"strict-origin-when-cross-origin\"
</IfModule>
";
        
        file_put_contents($project_path . '\\.htaccess', $htaccess_content);
        $steps[] = '✅ .htaccess file created for HTTPS redirect';
        
        // Step 7: Check if XAMPP SSL certificates exist
        $ssl_cert = $xampp_path . '\\apache\\conf\\ssl.crt\\server.crt';
        $ssl_key = $xampp_path . '\\apache\\conf\\ssl.key\\server.key';
        
        if (file_exists($ssl_cert) && file_exists($ssl_key)) {
            $steps[] = '✅ XAMPP SSL certificates found';
        } else {
            $steps[] = '⚠️ XAMPP SSL certificates not found - will create basic ones';
            createBasicSSLCerts($xampp_path);
        }
        
        // Step 8: Create restart script
        $restart_script = "@echo off
echo Restarting XAMPP Apache...
echo.
net stop Apache2.4
timeout /t 2 /nobreak > nul
net start Apache2.4
echo.
echo Apache restarted! 
echo Now visit: https://localhost/1992
echo.
pause";
        
        file_put_contents($project_path . '\\restart_apache.bat', $restart_script);
        $steps[] = '✅ Apache restart script created';
        
        $success = true;
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}

function createBasicSSLCerts($xampp_path) {
    // Create basic SSL certificate files if they don't exist
    $ssl_dir = $xampp_path . '\\apache\\conf\\ssl.crt';
    $key_dir = $xampp_path . '\\apache\\conf\\ssl.key';
    
    if (!is_dir($ssl_dir)) mkdir($ssl_dir, 0755, true);
    if (!is_dir($key_dir)) mkdir($key_dir, 0755, true);
    
    // Basic certificate content (for development only)
    $cert_content = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKL0UG+jkjkqMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkJEMQswCQYDVQQIDAJESzEOMAwGA1UEBwwFRGhha2ExGTAXBgNVBAoMEEZy
aWVuZHMgT3JnYW5pemF0aW9uMB4XDTI0MDEwMTAwMDAwMFoXDTI1MDEwMTAwMDAw
MFowRTELMAkGA1UEBhMCQkQxCzAJBgNVBAgMAkRLMQ4wDAYDVQQHDAVEaGFrYTEZ
MBcGA1UECgwQRnJpZW5kcyBPcmdhbml6YXRpb24wggEiMA0GCSqGSIb3DQEBAQUA
A4IBDwAwggEKAoIBAQC7VJTUt9Us8cKBwjnOTnc9wOU2pbwdXhp+0b3rD+n7qQxU
UuROlUcWYmkPkjZxwku8nkS6kHnFiCQXTz0WePWqKsMpkjcOgxcSaMkrXJ3Je7cG
cMhqiHXgQWBQmQWubdJ2TXBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPc
fwxbAiPeQoDvfez5Q3ujAlHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeI
HlIcGpWMuF3F05k1rl+dVlB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlB
nDI0WSiP+CqCwA+m5Ag1oQaMeANmwtxYEybY5QIDAQABMA0GCSqGSIb3DQEBCwUA
A4IBAQBYWs/ny4nGNFpjEEkKMfnaHViFAFWODDuj2OlTlZs2+Tv9rBrxmf8L5s/+
OQNr5Aw+cMEbK4hyBcm2L1c1gKvRPiSHPwdBZ4+VpEr/jxdPhB9P6VtVbNeBO4Mi
6wc8jmjW+jHBHwxHBcCGlVQMOchReYgIcGcQcwjV6hqyh+aMjEGb4CQrAV/dxgHB
OTxIyuIOiuZ+WEFRQMDjXSlKMxPKXnH5gDKy4ot5SWrtAh7Cc+lpiS8FRTfBa54T
6hOOHS6HcCxHPY3ItFVUcUtTurGLOw==
-----END CERTIFICATE-----";
    
    $key_content = "-----BEGIN PRIVATE KEY-----
MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC7VJTUt9Us8cKB
wjnOTnc9wOU2pbwdXhp+0b3rD+n7qQxUUuROlUcWYmkPkjZxwku8nkS6kHnFiCQX
Tz0WePWqKsMpkjcOgxcSaMkrXJ3Je7cGcMhqiHXgQWBQmQWubdJ2TXBYdGFBBngB
xLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQoDvfez5Q3ujAlHaGGfLnvtO
5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMuF3F05k1rl+dVlB5uEHg7aTp
+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+CqCwA+m5Ag1oQaMeANmwtxY
EybY5QIDAQABAoIBAQCrXF8/f9/cGuGmN4Hrkgexm2/kxSKgCxZOFNXXxLWMJI0E
nxgtVFFIcJ2PJM+QRQDIVSOBHjuP+bvREKinBfcH0dHXzeQb1c6T6t/LjVHGS6jn
OeE2cDV69HS6ia4giKhTHn3ZUcjKBcHCNlLt+aAzVpAI0YfGXA+K8Yd4BKOVr+1Z
nxmHjEqJGgKBgQDWxGcwlkjGiMOgxcSaMkrXJ3Je7cGcMhqiHXgQWBQmQWubdJ2T
XBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQoDvfez5Q3ujA
lHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMuF3F05k1rl+dV
lB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+CqCwA+m5Ag1o
QaMeANmwtxYEybY5QIDAQABAoIBAQCrXF8/f9/cGuGmN4Hrkgexm2/kxSKgCxZO
FNXXxLWMJI0EnxgtVFFIcJ2PJM+QRQDIVSOBHjuP+bvREKinBfcH0dHXzeQb1c6T
6t/LjVHGS6jnOeE2cDV69HS6ia4giKhTHn3ZUcjKBcHCNlLt+aAzVpAI0YfGXA+K
8Yd4BKOVr+1ZnxmHjEqJGgKBgQDWxGcwlkjGiMOgxcSaMkrXJ3Je7cGcMhqiHXgQ
WBQmQWubdJ2TXBYdGFBBngBxLLdcvEmQhJw61tqFxigSBqP1dzflIPcfwxbAiPeQ
oDvfez5Q3ujAlHaGGfLnvtO5GZh+ksWdoB0c2ZcYFBEWQNcF7R9kkeIHlIcGpWMu
F3F05k1rl+dVlB5uEHg7aTp+5VhzPqxI+BqZXeqf+LjLHGhwcXuaQlBnDI0WSiP+
CqCwA+m5Ag1oQaMeANmwtxYEybY5QIDAQAB
-----END PRIVATE KEY-----";
    
    file_put_contents($ssl_dir . '\\server.crt', $cert_content);
    file_put_contents($key_dir . '\\server.key', $key_content);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAMPP HTTPS Enable - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="gradient-bg text-white py-8">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h1 class="text-3xl font-bold mb-2">
                <i class="fas fa-shield-alt mr-3"></i>
                XAMPP HTTPS Enable
            </h1>
            <p class="text-green-100">OpenSSL ছাড়াই XAMPP এ HTTPS চালু করুন</p>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 py-8">
        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    সমস্যা হয়েছে:
                </h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    HTTPS সফলভাবে Enable করা হয়েছে!
                </h4>
                <div class="space-y-2">
                    <?php foreach ($steps as $step): ?>
                        <div><?php echo $step; ?></div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center text-green-600">
                    <i class="fas fa-rocket mr-2"></i>
                    এখন Apache Restart করুন
                </h2>

                <div class="space-y-6">
                    <!-- Method 1: Automatic -->
                    <div class="border-l-4 border-green-500 pl-4">
                        <h3 class="text-lg font-semibold text-green-600 mb-2">
                            পদ্ধতি ১: Automatic Restart (সুপারিশকৃত)
                        </h3>
                        <p class="text-gray-700 mb-3">
                            restart_apache.bat ফাইল Administrator হিসেবে চালান:
                        </p>
                        <button onclick="runRestartScript()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg">
                            <i class="fas fa-play mr-2"></i>
                            Apache Restart করুন
                        </button>
                    </div>

                    <!-- Method 2: Manual -->
                    <div class="border-l-4 border-blue-500 pl-4">
                        <h3 class="text-lg font-semibold text-blue-600 mb-2">
                            পদ্ধতি ২: Manual Restart
                        </h3>
                        <div class="text-gray-700 space-y-2">
                            <p>1. XAMPP Control Panel খুলুন</p>
                            <p>2. Apache "Stop" করুন</p>
                            <p>3. ২-৩ সেকেন্ড অপেক্ষা করুন</p>
                            <p>4. Apache "Start" করুন</p>
                            <p>5. Port 443 (SSL) চালু আছে কিনা চেক করুন</p>
                        </div>
                    </div>

                    <!-- Test Links -->
                    <div class="border-l-4 border-purple-500 pl-4">
                        <h3 class="text-lg font-semibold text-purple-600 mb-2">
                            Test করুন
                        </h3>
                        <div class="space-y-2">
                            <div>
                                <a href="https://localhost/1992" target="_blank" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    https://localhost/1992 (HTTPS)
                                </a>
                                <span class="text-sm text-gray-500 ml-2">- Green lock দেখতে পাবেন</span>
                            </div>
                            <div>
                                <a href="http://localhost/1992" target="_blank" class="inline-flex items-center text-gray-600 hover:text-gray-700">
                                    <i class="fas fa-external-link-alt mr-2"></i>
                                    http://localhost/1992 (HTTP)
                                </a>
                                <span class="text-sm text-gray-500 ml-2">- Auto redirect to HTTPS</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8 text-center">
                    <a href="setup_live_payment.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Live Payment Setup এ যান
                    </a>
                </div>
            </div>

        <?php else: ?>
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">
                    <i class="fas fa-cog mr-2 text-green-600"></i>
                    XAMPP HTTPS Enable করুন
                </h2>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        এই পদ্ধতির সুবিধা:
                    </h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• OpenSSL install করার প্রয়োজন নেই</li>
                        <li>• XAMPP এর built-in SSL certificate ব্যবহার করে</li>
                        <li>• Automatic HTTPS redirect setup করে</li>
                        <li>• ১ ক্লিকেই সব কিছু configure হয়ে যায়</li>
                    </ul>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-yellow-800 mb-2">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        প্রয়োজনীয় শর্ত:
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• XAMPP properly installed হতে হবে</li>
                        <li>• Apache running থাকতে হবে</li>
                        <li>• Administrator access প্রয়োজন</li>
                        <li>• Port 80 ও 443 free থাকতে হবে</li>
                    </ul>
                </div>

                <form method="POST" class="text-center">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-8 rounded-lg text-lg">
                        <i class="fas fa-shield-alt mr-2"></i>
                        HTTPS Enable করুন
                    </button>
                </form>

                <div class="mt-6 text-center text-sm text-gray-500">
                    <p>
                        <i class="fas fa-clock mr-1"></i>
                        Setup time: ১-২ মিনিট
                    </p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Alternative Methods -->
        <div class="mt-8 bg-gray-100 rounded-lg p-6">
            <h3 class="text-lg font-semibold mb-4">
                <i class="fas fa-tools mr-2 text-blue-600"></i>
                বিকল্প পদ্ধতি
            </h3>
            <div class="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-medium mb-2">যদি এই পদ্ধতি কাজ না করে:</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• <a href="setup_ssl_localhost.php" class="text-blue-600 hover:underline">OpenSSL Setup চেষ্টা করুন</a></li>
                        <li>• Manual SSL certificate তৈরি করুন</li>
                        <li>• XAMPP reinstall করুন</li>
                        <li>• Port conflicts চেক করুন</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Production এর জন্য:</h4>
                    <ul class="space-y-1 text-gray-600">
                        <li>• <a href="setup_live_payment.php?sslsetup=1" class="text-blue-600 hover:underline">Cloudflare SSL ব্যবহার করুন</a></li>
                        <li>• Let's Encrypt certificate নিন</li>
                        <li>• Paid SSL certificate কিনুন</li>
                        <li>• <a href="XAMPP_SSL_SETUP.md" class="text-blue-600 hover:underline">বিস্তারিত গাইড দেখুন</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function runRestartScript() {
            alert('restart_apache.bat ফাইলে right-click করে "Run as administrator" select করুন।\n\nঅথবা Command Prompt (Admin) এ যান এবং চালান:\nnet stop Apache2.4\nnet start Apache2.4');
        }
    </script>
</body>
</html>
