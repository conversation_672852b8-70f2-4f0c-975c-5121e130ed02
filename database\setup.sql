-- Friends Organization Database Setup
-- Run this script in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Members table
CREATE TABLE IF NOT EXISTS members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20) NOT NULL,
  address TEXT NOT NULL,
  ssc_roll VARCHAR(50) NOT NULL,
  ssc_year VARCHAR(10) NOT NULL,
  ssc_board VARCHAR(50) NOT NULL,
  monthly_amount INTEGER NOT NULL CHECK (monthly_amount >= 100),
  photo_url VARCHAR(500),
  marksheet_url VARCHAR(500),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  member_id UUID REFERENCES members(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL CHECK (amount > 0),
  type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'profit', 'withdrawal')),
  description TEXT,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  payment_method VARCHAR(50),
  transaction_id VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  total_investment INTEGER NOT NULL CHECK (total_investment > 0),
  expected_return DECIMAL(5,2) CHECK (expected_return >= 0),
  start_date DATE NOT NULL,
  end_date DATE,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Project investments table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS project_investments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  member_id UUID REFERENCES members(id) ON DELETE CASCADE,
  investment_amount INTEGER NOT NULL CHECK (investment_amount > 0),
  profit_amount INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, member_id)
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  member_id UUID REFERENCES members(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_members_email ON members(email);
CREATE INDEX IF NOT EXISTS idx_members_status ON members(status);
CREATE INDEX IF NOT EXISTS idx_transactions_member_id ON transactions(member_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_project_investments_project_id ON project_investments(project_id);
CREATE INDEX IF NOT EXISTS idx_project_investments_member_id ON project_investments(member_id);
CREATE INDEX IF NOT EXISTS idx_notifications_member_id ON notifications(member_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES 
('member-photos', 'member-photos', true),
('member-documents', 'member-documents', true)
ON CONFLICT (id) DO NOTHING;

-- Enable Row Level Security
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for members table
CREATE POLICY "Public members are viewable by everyone" ON members
  FOR SELECT USING (status = 'approved');

CREATE POLICY "Users can insert their own member record" ON members
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own member record" ON members
  FOR UPDATE USING (auth.uid()::text = id::text);

-- RLS Policies for transactions table
CREATE POLICY "Users can view their own transactions" ON transactions
  FOR SELECT USING (auth.uid()::text = member_id::text);

CREATE POLICY "Users can insert their own transactions" ON transactions
  FOR INSERT WITH CHECK (auth.uid()::text = member_id::text);

-- RLS Policies for projects table
CREATE POLICY "Active projects are viewable by everyone" ON projects
  FOR SELECT USING (status = 'active');

-- RLS Policies for project_investments table
CREATE POLICY "Users can view their own investments" ON project_investments
  FOR SELECT USING (auth.uid()::text = member_id::text);

-- RLS Policies for notifications table
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (auth.uid()::text = member_id::text);

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (auth.uid()::text = member_id::text);

-- Functions and triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON members
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate member's total balance
CREATE OR REPLACE FUNCTION get_member_balance(member_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    total_deposits INTEGER;
    total_profits INTEGER;
    total_withdrawals INTEGER;
BEGIN
    -- Calculate total deposits
    SELECT COALESCE(SUM(amount), 0) INTO total_deposits
    FROM transactions
    WHERE member_id = member_uuid AND type = 'deposit' AND status = 'completed';
    
    -- Calculate total profits
    SELECT COALESCE(SUM(amount), 0) INTO total_profits
    FROM transactions
    WHERE member_id = member_uuid AND type = 'profit' AND status = 'completed';
    
    -- Calculate total withdrawals
    SELECT COALESCE(SUM(amount), 0) INTO total_withdrawals
    FROM transactions
    WHERE member_id = member_uuid AND type = 'withdrawal' AND status = 'completed';
    
    RETURN total_deposits + total_profits - total_withdrawals;
END;
$$ LANGUAGE plpgsql;

-- Function to get organization statistics
CREATE OR REPLACE FUNCTION get_organization_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_members', (SELECT COUNT(*) FROM members WHERE status = 'approved'),
        'pending_members', (SELECT COUNT(*) FROM members WHERE status = 'pending'),
        'total_deposits', (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed'),
        'total_profits', (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'profit' AND status = 'completed'),
        'active_projects', (SELECT COUNT(*) FROM projects WHERE status = 'active'),
        'monthly_deposits', (
            SELECT COALESCE(SUM(amount), 0) 
            FROM transactions 
            WHERE type = 'deposit' 
            AND status = 'completed' 
            AND created_at >= date_trunc('month', CURRENT_DATE)
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data (optional)
-- Sample admin user (password: admin123)
INSERT INTO admin_users (email, password_hash, full_name, role) VALUES
('<EMAIL>', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'System Admin', 'super_admin')
ON CONFLICT (email) DO NOTHING;

-- Sample projects
INSERT INTO projects (name, description, total_investment, expected_return, start_date, status) VALUES
('ছোট ব্যবসা বিনিয়োগ', 'স্থানীয় ছোট ব্যবসায় বিনিয়োগ প্রকল্প', 500000, 15.00, '2024-01-01', 'active'),
('কৃষি প্রকল্প', 'আধুনিক কৃষি পদ্ধতিতে বিনিয়োগ', 300000, 12.50, '2024-02-01', 'active'),
('প্রযুক্তি স্টার্টআপ', 'নতুন প্রযুক্তি কোম্পানিতে বিনিয়োগ', 800000, 20.00, '2024-03-01', 'active')
ON CONFLICT DO NOTHING;

-- Create a view for member dashboard
CREATE OR REPLACE VIEW member_dashboard AS
SELECT 
    m.id,
    m.full_name,
    m.email,
    m.phone,
    m.monthly_amount,
    m.status,
    m.created_at as member_since,
    get_member_balance(m.id) as current_balance,
    (SELECT COUNT(*) FROM transactions t WHERE t.member_id = m.id AND t.status = 'completed') as total_transactions,
    (SELECT COALESCE(SUM(amount), 0) FROM transactions t WHERE t.member_id = m.id AND t.type = 'deposit' AND t.status = 'completed') as total_deposits,
    (SELECT COALESCE(SUM(amount), 0) FROM transactions t WHERE t.member_id = m.id AND t.type = 'profit' AND t.status = 'completed') as total_profits
FROM members m
WHERE m.status = 'approved';

COMMENT ON TABLE members IS 'Stores member information and registration details';
COMMENT ON TABLE transactions IS 'Stores all financial transactions';
COMMENT ON TABLE projects IS 'Stores investment project information';
COMMENT ON TABLE project_investments IS 'Links members to their project investments';
COMMENT ON TABLE admin_users IS 'Stores admin user credentials';
COMMENT ON TABLE notifications IS 'Stores user notifications';

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
