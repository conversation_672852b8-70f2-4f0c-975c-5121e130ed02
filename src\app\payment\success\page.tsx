'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { CheckCircle, Download, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface TransactionDetails {
  id: string
  amount: number
  status: string
  createdAt: string
  description: string
}

export default function PaymentSuccess() {
  const searchParams = useSearchParams()
  const transactionId = searchParams.get('transactionId')
  const [transaction, setTransaction] = useState<TransactionDetails | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (transactionId) {
      fetchTransactionDetails()
    }
  }, [transactionId])

  const fetchTransactionDetails = async () => {
    try {
      const response = await fetch(`/api/payment?transactionId=${transactionId}`)
      const data = await response.json()
      
      if (data.success) {
        setTransaction(data.transaction)
      }
    } catch (error) {
      console.error('Error fetching transaction:', error)
    } finally {
      setLoading(false)
    }
  }

  const downloadReceipt = () => {
    if (!transaction) return

    const receiptContent = `
পেমেন্ট রসিদ - বন্ধুদের সংস্থা
=====================================

লেনদেন নম্বর: ${transactionId}
পরিমাণ: ৳${transaction.amount.toLocaleString()}
তারিখ: ${new Date(transaction.createdAt).toLocaleDateString('bn-BD')}
বিবরণ: ${transaction.description}
অবস্থা: সফল

ধন্যবাদ!
বন্ধুদের সংস্থা
    `

    const blob = new Blob([receiptContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `receipt-${transactionId}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto px-4 py-12">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto px-4 py-12">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        <CheckCircle className="w-20 h-20 text-green-600 mx-auto mb-6" />
        
        <h1 className="text-3xl font-bold text-green-600 mb-4">
          পেমেন্ট সফল হয়েছে!
        </h1>
        
        <p className="text-gray-600 mb-8">
          আপনার পেমেন্ট সফলভাবে সম্পন্ন হয়েছে। ধন্যবাদ!
        </p>

        {transaction && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">লেনদেনের বিবরণ</h2>
            <div className="space-y-3 text-left">
              <div className="flex justify-between">
                <span className="text-gray-600">লেনদেন নম্বর:</span>
                <span className="font-mono font-medium">{transactionId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">পরিমাণ:</span>
                <span className="font-bold text-green-600">৳{transaction.amount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">তারিখ:</span>
                <span className="font-medium">
                  {new Date(transaction.createdAt).toLocaleDateString('bn-BD', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">বিবরণ:</span>
                <span className="font-medium">{transaction.description}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">অবস্থা:</span>
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                  সফল
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <button
            onClick={downloadReceipt}
            className="w-full btn-secondary flex items-center justify-center"
          >
            <Download className="w-5 h-5 mr-2" />
            রসিদ ডাউনলোড করুন
          </button>
          
          <div className="flex space-x-4">
            <Link href="/dashboard" className="flex-1 btn-primary">
              ড্যাশবোর্ডে যান
            </Link>
            <Link href="/payment" className="flex-1 btn-secondary">
              আরেকটি পেমেন্ট
            </Link>
          </div>
          
          <Link href="/" className="inline-flex items-center text-primary-600 hover:text-primary-700">
            <ArrowLeft className="w-4 h-4 mr-1" />
            হোম পেজে ফিরে যান
          </Link>
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>গুরুত্বপূর্ণ:</strong> এই রসিদটি সংরক্ষণ করুন। ভবিষ্যতে যেকোনো সমস্যার জন্য 
            লেনদেন নম্বর প্রয়োজন হতে পারে।
          </p>
        </div>
      </div>
    </div>
  )
}
