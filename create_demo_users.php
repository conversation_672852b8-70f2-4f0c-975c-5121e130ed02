<?php
/**
 * <PERSON>reate Demo Users for Login
 * Run this to create demo login accounts
 */

require_once 'config/database.php';

try {
    // Create demo users with proper password hashing
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $member_password = password_hash('member123', PASSWORD_DEFAULT);
    
    // Check if members table exists and has the right structure
    $check_table = "SHOW TABLES LIKE 'members'";
    $result = $db->query($check_table);
    
    if ($result->rowCount() == 0) {
        // Create members table if it doesn't exist
        $create_members = "
        CREATE TABLE members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            phone VARCHAR(20),
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'member') DEFAULT 'member',
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $db->exec($create_members);
        echo "<p style='color: green;'>✅ Members table created</p>";
    }
    
    // Insert demo users
    $insert_users = "
    INSERT INTO members (name, email, phone, password, role, status, created_at) VALUES
    ('Admin User', '<EMAIL>', '01712345678', ?, 'admin', 'active', NOW()),
    ('Member User', '<EMAIL>', '01712345679', ?, 'member', 'active', NOW()),
    ('John Doe', '<EMAIL>', '01712345680', ?, 'member', 'active', NOW()),
    ('Jane Smith', '<EMAIL>', '01712345681', ?, 'member', 'active', NOW())
    ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    updated_at = NOW()
    ";
    
    $stmt = $db->prepare($insert_users);
    $stmt->execute([$admin_password, $member_password, $member_password, $member_password]);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ Demo Users Created Successfully!</h3>";
    echo "<p>You can now login with these credentials:</p>";
    echo "<div style='background: white; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔑 Login Credentials:</h4>";
    echo "<p><strong>Admin Account:</strong><br>";
    echo "📧 Email: <EMAIL><br>";
    echo "🔒 Password: admin123</p>";
    echo "<p><strong>Member Account:</strong><br>";
    echo "📧 Email: <EMAIL><br>";
    echo "🔒 Password: member123</p>";
    echo "</div>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
    echo "</div>";
    
    // Verify users were created
    $verify_query = "SELECT id, name, email, role FROM members WHERE email IN ('<EMAIL>', '<EMAIL>')";
    $stmt = $db->query($verify_query);
    $users = $stmt->fetchAll();
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>📊 Verification:</h4>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px; border: 1px solid #ddd;'>ID</th><th style='padding: 8px; border: 1px solid #ddd;'>Name</th><th style='padding: 8px; border: 1px solid #ddd;'>Email</th><th style='padding: 8px; border: 1px solid #ddd;'>Role</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $user['id'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $user['role'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Error Creating Demo Users</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h4>🔧 Troubleshooting:</h4>";
    echo "<ol>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check database connection in config/database.php</li>";
    echo "<li>Run setup.php first to create database</li>";
    echo "<li>Check if database 'friends_organization' exists</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Demo Users - বন্ধুদের সংস্থা</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .icon {
            text-align: center;
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }
        .quick-links {
            text-align: center;
            margin-top: 30px;
        }
        .quick-links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .quick-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">👥</div>
        <h2>Demo Users Setup</h2>
        
        <!-- PHP output appears here -->
        
        <div class="quick-links">
            <a href="login.php">🔐 Login Page</a>
            <a href="dashboard.php">📊 Dashboard</a>
            <a href="setup.php">⚙️ Database Setup</a>
            <a href="index.php">🏠 Home</a>
        </div>
    </div>
</body>
</html>
