# SSL Certificate Setup Guide for localhost/1992/


## Cloudflare SSL Setup (Free)

### Steps:

1. **Create Cloudflare Account:**
- Go to: https://cloudflare.com
- Sign up for free account

2. **Add Your Domain:**
- Click 'Add a Site'
- Enter: localhost/1992/
- Choose Free plan

3. **Update Nameservers:**
- Copy Cloudflare nameservers
- Update at your domain registrar
- Wait 24-48 hours for propagation

4. **Enable SSL:**
- Go to SSL/TLS tab
- Set to 'Flexible' or 'Full'
- Enable 'Always Use HTTPS'

5. **Configure Origin Server:**
```apache
# Add to .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

6. **Verify SSL:**
- Visit: https://localhost/1992/
- Check Cloudflare SSL status
