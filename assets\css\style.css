/* Custom CSS for Friends Organization */

/* Bengali Font */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Bengali', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Custom Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in {
    animation: fadeIn 1s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.8s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.8s ease-out;
}

/* Custom Button Styles */
.btn-primary {
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    text-decoration: none;
    color: white;
}

.btn-secondary {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    padding: 12px 24px;
    border-radius: 8px;
    color: #475569;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #e2e8f0;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    text-decoration: none;
    color: #334155;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.hero-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

/* Navigation */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(59, 130, 246, 0.95);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #bfdbfe !important;
    transform: translateY(-1px);
}

/* Statistics Section */
.stats-section {
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
    position: relative;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 50px 50px;
}

.stat-item {
    text-align: center;
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 8px;
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Feature Cards */
.feature-card {
    text-align: center;
    padding: 30px 20px;
    border-radius: 16px;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    border: 1px solid #f1f5f9;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
}

.feature-icon.blue {
    background: linear-gradient(45deg, #dbeafe, #bfdbfe);
    color: #3b82f6;
}

.feature-icon.green {
    background: linear-gradient(45deg, #dcfce7, #bbf7d0);
    color: #10b981;
}

.feature-icon.purple {
    background: linear-gradient(45deg, #f3e8ff, #e9d5ff);
    color: #8b5cf6;
}

.feature-icon.yellow {
    background: linear-gradient(45deg, #fef3c7, #fde68a);
    color: #f59e0b;
}

/* Process Steps */
.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 2rem;
    font-weight: 700;
    color: white;
    position: relative;
    z-index: 2;
}

.step-number.blue {
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
}

.step-number.green {
    background: linear-gradient(45deg, #10b981, #059669);
}

.step-number.purple {
    background: linear-gradient(45deg, #8b5cf6, #7c3aed);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.footer-link {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #f9fafb;
    text-decoration: none;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    color: #9ca3af;
    text-decoration: none;
    transition: all 0.3s ease;
    margin: 0 8px;
}

.social-icon:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-bg h1 {
        font-size: 2.5rem;
    }
    
    .hero-bg p {
        font-size: 1.1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .feature-card {
        margin-bottom: 20px;
    }
    
    .btn-primary,
    .btn-secondary {
        display: block;
        text-align: center;
        margin: 10px 0;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.error {
    border-color: #ef4444;
    background: #fef2f2;
}

.error-message {
    color: #ef4444;
    font-size: 14px;
    margin-top: 4px;
}

/* Success Message */
.alert-success {
    background: #d1fae5;
    border: 1px solid #a7f3d0;
    color: #065f46;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #991b1b;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }
.mb-5 { margin-bottom: 40px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }
.mt-5 { margin-top: 40px; }
