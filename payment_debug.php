<?php
/**
 * Payment Gateway Debug Tool
 * Tests SSLCommerz connection and configuration
 */

require_once 'config/database.php';
require_once 'config/payment_config.php';

$test_results = [];
$errors = [];

// Test 1: Check configuration
$test_results['config'] = [
    'PAYMENT_ENVIRONMENT' => PAYMENT_ENVIRONMENT,
    'SSLCZ_STORE_ID' => SSLCZ_STORE_ID,
    'SSLCZ_STORE_PASSWORD' => substr(SSLCZ_STORE_PASSWORD, 0, 3) . '***',
    'SSLCZ_REQUEST_URL' => SSLCZ_REQUEST_URL,
    'APP_URL' => defined('APP_URL') ? APP_URL : 'NOT DEFINED'
];

// Test 2: Check cURL availability
$test_results['curl'] = function_exists('curl_init') ? 'Available' : 'NOT AVAILABLE';

// Test 3: Test SSLCommerz connection
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_connection'])) {
    try {
        $test_data = [
            'store_id' => SSLCZ_STORE_ID,
            'store_passwd' => SSLCZ_STORE_PASSWORD,
            'total_amount' => 10,
            'currency' => 'BDT',
            'tran_id' => 'TEST_' . time(),
            'success_url' => (defined('APP_URL') ? APP_URL : 'http://localhost/1992') . '/payment_success.php',
            'fail_url' => (defined('APP_URL') ? APP_URL : 'http://localhost/1992') . '/payment_fail.php',
            'cancel_url' => (defined('APP_URL') ? APP_URL : 'http://localhost/1992') . '/payment_cancel.php',
            'cus_name' => 'Test Customer',
            'cus_email' => '<EMAIL>',
            'cus_phone' => '01712345678',
            'cus_add1' => 'Test Address',
            'cus_city' => 'Dhaka',
            'cus_country' => 'Bangladesh',
            'product_name' => 'Test Payment',
            'product_category' => 'Service',
            'product_profile' => 'general'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, SSLCZ_REQUEST_URL);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $test_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        $test_results['connection'] = [
            'http_code' => $http_code,
            'curl_error' => $curl_error,
            'response_length' => strlen($response),
            'response' => $response ? json_decode($response, true) : null
        ];
        
    } catch (Exception $e) {
        $errors[] = 'Connection test failed: ' . $e->getMessage();
    }
}

// Test 4: Check database connection
try {
    if ($db) {
        $stmt = $db->query("SELECT COUNT(*) as count FROM transactions");
        $result = $stmt->fetch();
        $test_results['database'] = 'Connected - ' . $result['count'] . ' transactions';
    } else {
        $test_results['database'] = 'NOT CONNECTED';
    }
} catch (Exception $e) {
    $test_results['database'] = 'ERROR: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Debug Tool - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="gradient-bg text-white py-8">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h1 class="text-3xl font-bold mb-2">
                <i class="fas fa-bug mr-3"></i>
                Payment Gateway Debug Tool
            </h1>
            <p class="text-red-100">পেমেন্ট গেটওয়ে সমস্যা নির্ণয় ও সমাধান</p>
        </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 py-8">
        <!-- Configuration Status -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-cog mr-2 text-blue-600"></i>
                Configuration Status
            </h2>
            
            <div class="grid md:grid-cols-2 gap-4">
                <?php foreach ($test_results['config'] as $key => $value): ?>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium"><?php echo $key; ?>:</span>
                        <span class="<?php echo ($key == 'APP_URL' && $value == 'NOT DEFINED') ? 'text-red-600' : 'text-green-600'; ?>">
                            <?php echo htmlspecialchars($value); ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- System Status -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-server mr-2 text-green-600"></i>
                System Status
            </h2>
            
            <div class="space-y-3">
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span class="font-medium">cURL Extension:</span>
                    <span class="<?php echo $test_results['curl'] == 'Available' ? 'text-green-600' : 'text-red-600'; ?>">
                        <i class="fas fa-<?php echo $test_results['curl'] == 'Available' ? 'check' : 'times'; ?> mr-1"></i>
                        <?php echo $test_results['curl']; ?>
                    </span>
                </div>
                
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span class="font-medium">Database Connection:</span>
                    <span class="<?php echo strpos($test_results['database'], 'Connected') !== false ? 'text-green-600' : 'text-red-600'; ?>">
                        <i class="fas fa-<?php echo strpos($test_results['database'], 'Connected') !== false ? 'check' : 'times'; ?> mr-1"></i>
                        <?php echo htmlspecialchars($test_results['database']); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Connection Test -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-wifi mr-2 text-purple-600"></i>
                SSLCommerz Connection Test
            </h2>
            
            <?php if (isset($test_results['connection'])): ?>
                <div class="mb-4 p-4 <?php echo $test_results['connection']['http_code'] == 200 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'; ?> border rounded-lg">
                    <h4 class="font-semibold mb-2">Test Results:</h4>
                    <div class="space-y-2 text-sm">
                        <div><strong>HTTP Code:</strong> <?php echo $test_results['connection']['http_code']; ?></div>
                        <div><strong>cURL Error:</strong> <?php echo $test_results['connection']['curl_error'] ?: 'None'; ?></div>
                        <div><strong>Response Length:</strong> <?php echo $test_results['connection']['response_length']; ?> bytes</div>
                        
                        <?php if ($test_results['connection']['response']): ?>
                            <div><strong>Response Status:</strong> 
                                <span class="<?php echo $test_results['connection']['response']['status'] == 'SUCCESS' ? 'text-green-600' : 'text-red-600'; ?>">
                                    <?php echo $test_results['connection']['response']['status'] ?? 'Unknown'; ?>
                                </span>
                            </div>
                            
                            <?php if (isset($test_results['connection']['response']['failedreason'])): ?>
                                <div><strong>Failed Reason:</strong> 
                                    <span class="text-red-600"><?php echo $test_results['connection']['response']['failedreason']; ?></span>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <button type="submit" name="test_connection" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg">
                    <i class="fas fa-play mr-2"></i>
                    SSLCommerz Connection Test করুন
                </button>
            </form>
        </div>

        <!-- Quick Fixes -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-tools mr-2 text-orange-600"></i>
                Quick Fixes
            </h2>
            
            <div class="grid md:grid-cols-2 gap-4">
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-semibold text-blue-600 mb-2">Environment Fix</h4>
                    <p class="text-sm text-gray-600 mb-3">Switch to Sandbox for testing</p>
                    <a href="?fix=sandbox" class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                        Switch to Sandbox
                    </a>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-semibold text-green-600 mb-2">Database Fix</h4>
                    <p class="text-sm text-gray-600 mb-3">Reset database if needed</p>
                    <a href="setup.php" class="bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                        Database Setup
                    </a>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-semibold text-purple-600 mb-2">SSL Fix</h4>
                    <p class="text-sm text-gray-600 mb-3">Setup HTTPS for payment</p>
                    <a href="simple_ssl_setup.php" class="bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700">
                        SSL Setup
                    </a>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-semibold text-red-600 mb-2">Live Setup</h4>
                    <p class="text-sm text-gray-600 mb-3">Configure live payment</p>
                    <a href="setup_live_payment.php" class="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700">
                        Live Setup
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-6">
                <h4 class="font-bold mb-2">Errors Found:</h4>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Back to Payment -->
        <div class="text-center">
            <a href="payment.php" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg">
                <i class="fas fa-arrow-left mr-2"></i>
                পেমেন্ট পেজে ফিরে যান
            </a>
        </div>
    </div>

    <?php
    // Handle quick fixes
    if (isset($_GET['fix'])) {
        if ($_GET['fix'] == 'sandbox') {
            // Switch to sandbox mode
            $config_content = file_get_contents('config/payment_config.php');
            $config_content = str_replace("define('PAYMENT_ENVIRONMENT', 'LIVE');", "define('PAYMENT_ENVIRONMENT', 'SANDBOX');", $config_content);
            file_put_contents('config/payment_config.php', $config_content);
            
            echo "<script>
                alert('Environment switched to SANDBOX mode for testing.');
                window.location.href = 'payment_debug.php';
            </script>";
        }
    }
    ?>
</body>
</html>
