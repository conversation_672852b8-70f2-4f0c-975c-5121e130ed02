-- Friends Organization Database Schema
-- MySQL Database for <PERSON>AMPP

CREATE DATABASE IF NOT EXISTS friends_organization CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE friends_organization;

-- Members table
CREATE TABLE IF NOT EXISTS members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    ssc_roll VARCHAR(50) NOT NULL,
    ssc_year VARCHAR(10) NOT NULL,
    ssc_board VARCHAR(50) NOT NULL,
    monthly_amount DECIMAL(10,2) NOT NULL CHECK (monthly_amount >= 100),
    photo_url VARCHAR(500),
    marksheet_url VARCHAR(500),
    password_hash VARCHAR(255),
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    type ENUM('deposit', 'profit', 'withdrawal') NOT NULL,
    description TEXT,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    reference_number VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    INDEX idx_member_id (member_id),
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    total_investment DECIMAL(15,2) NOT NULL CHECK (total_investment > 0),
    expected_return DECIMAL(5,2) CHECK (expected_return >= 0),
    actual_return DECIMAL(5,2) DEFAULT 0,
    start_date DATE NOT NULL,
    end_date DATE,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_start_date (start_date)
);

-- Project investments table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS project_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    member_id INT NOT NULL,
    investment_amount DECIMAL(10,2) NOT NULL CHECK (investment_amount > 0),
    profit_amount DECIMAL(10,2) DEFAULT 0,
    profit_percentage DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    UNIQUE KEY unique_investment (project_id, member_id),
    INDEX idx_project_id (project_id),
    INDEX idx_member_id (member_id)
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role ENUM('admin', 'super_admin') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT,
    admin_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_member_id (member_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    user_type ENUM('member', 'admin') NOT NULL,
    action VARCHAR(255) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_user_type (user_type),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);

-- Monthly reports table
CREATE TABLE IF NOT EXISTS monthly_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    month INT NOT NULL,
    year INT NOT NULL,
    total_members INT DEFAULT 0,
    total_deposits DECIMAL(15,2) DEFAULT 0,
    total_profits DECIMAL(15,2) DEFAULT 0,
    total_withdrawals DECIMAL(15,2) DEFAULT 0,
    active_projects INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_month_year (month, year),
    INDEX idx_year (year),
    INDEX idx_month (month)
);

-- Insert default admin user
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'super_admin')
ON DUPLICATE KEY UPDATE username = username;

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, description) VALUES
('site_name', 'বন্ধুদের সংস্থা', 'Website name'),
('site_description', 'বন্ধুদের নিয়ে গঠিত সংস্থা যেখানে মাসিক সঞ্চয় ও লাভজনক বিনিয়োগ করা হয়', 'Website description'),
('contact_email', '<EMAIL>', 'Contact email address'),
('contact_phone', '+৮৮০১৭xxxxxxxx', 'Contact phone number'),
('min_monthly_amount', '500', 'Minimum monthly deposit amount'),
('max_monthly_amount', '50000', 'Maximum monthly deposit amount'),
('registration_fee', '100', 'One-time registration fee'),
('profit_distribution_day', '25', 'Day of month for profit distribution'),
('maintenance_mode', '0', 'Maintenance mode (0=off, 1=on)')
ON DUPLICATE KEY UPDATE setting_key = setting_key;

-- Insert sample projects
INSERT INTO projects (name, description, total_investment, expected_return, start_date, status) VALUES
('ছোট ব্যবসা বিনিয়োগ প্রকল্প', 'স্থানীয় ছোট ব্যবসায়ীদের সাথে অংশীদারিত্বের মাধ্যমে বিনিয়োগ। এই প্রকল্পে আমরা খুচরা ব্যবসা, খাদ্য প্রক্রিয়াকরণ এবং হস্তশিল্পে বিনিয়োগ করি।', 500000.00, 15.00, '2024-01-01', 'active'),
('কৃষি ও খামার প্রকল্প', 'আধুনিক কৃষি পদ্ধতি, মাছ চাষ এবং পোল্ট্রি ফার্মে বিনিয়োগ। জৈব সার উৎপাদন এবং কৃষি যন্ত্রপাতি ভাড়া দেওয়ার ব্যবসাও অন্তর্ভুক্ত।', 300000.00, 12.50, '2024-02-01', 'active'),
('প্রযুক্তি ও ডিজিটাল সেবা', 'ওয়েব ডেভেলপমেন্ট, মোবাইল অ্যাপ তৈরি, ডিজিটাল মার্কেটিং এবং অনলাইন শিক্ষা প্ল্যাটফর্মে বিনিয়োগ।', 800000.00, 20.00, '2024-03-01', 'active')
ON DUPLICATE KEY UPDATE name = name;

-- Create views for easier data access
CREATE OR REPLACE VIEW member_dashboard AS
SELECT 
    m.id,
    m.full_name,
    m.email,
    m.phone,
    m.monthly_amount,
    m.status,
    m.created_at as member_since,
    COALESCE(SUM(CASE WHEN t.type = 'deposit' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as total_deposits,
    COALESCE(SUM(CASE WHEN t.type = 'profit' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as total_profits,
    COALESCE(SUM(CASE WHEN t.type = 'withdrawal' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as total_withdrawals,
    (COALESCE(SUM(CASE WHEN t.type = 'deposit' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) + 
     COALESCE(SUM(CASE WHEN t.type = 'profit' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) - 
     COALESCE(SUM(CASE WHEN t.type = 'withdrawal' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0)) as current_balance,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as total_transactions
FROM members m
LEFT JOIN transactions t ON m.id = t.member_id
WHERE m.status = 'approved'
GROUP BY m.id, m.full_name, m.email, m.phone, m.monthly_amount, m.status, m.created_at;

-- Create view for organization statistics
CREATE OR REPLACE VIEW organization_stats AS
SELECT 
    (SELECT COUNT(*) FROM members WHERE status = 'approved') as total_members,
    (SELECT COUNT(*) FROM members WHERE status = 'pending') as pending_members,
    (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed') as total_deposits,
    (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'profit' AND status = 'completed') as total_profits,
    (SELECT COUNT(*) FROM projects WHERE status = 'active') as active_projects,
    (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE) AND YEAR(created_at) = YEAR(CURRENT_DATE)) as monthly_deposits,
    (SELECT COALESCE(SUM(total_investment), 0) FROM projects WHERE status = 'active') as total_active_investment;

-- Create stored procedures
DELIMITER //

-- Procedure to calculate member balance
CREATE PROCEDURE GetMemberBalance(IN member_id INT, OUT balance DECIMAL(10,2))
BEGIN
    DECLARE total_deposits DECIMAL(10,2) DEFAULT 0;
    DECLARE total_profits DECIMAL(10,2) DEFAULT 0;
    DECLARE total_withdrawals DECIMAL(10,2) DEFAULT 0;
    
    SELECT COALESCE(SUM(amount), 0) INTO total_deposits
    FROM transactions 
    WHERE member_id = member_id AND type = 'deposit' AND status = 'completed';
    
    SELECT COALESCE(SUM(amount), 0) INTO total_profits
    FROM transactions 
    WHERE member_id = member_id AND type = 'profit' AND status = 'completed';
    
    SELECT COALESCE(SUM(amount), 0) INTO total_withdrawals
    FROM transactions 
    WHERE member_id = member_id AND type = 'withdrawal' AND status = 'completed';
    
    SET balance = total_deposits + total_profits - total_withdrawals;
END //

-- Procedure to distribute profits
CREATE PROCEDURE DistributeProfits(IN project_id INT, IN total_profit DECIMAL(15,2))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE member_id INT;
    DECLARE investment_amount DECIMAL(10,2);
    DECLARE member_profit DECIMAL(10,2);
    DECLARE total_investment DECIMAL(15,2);
    
    DECLARE member_cursor CURSOR FOR 
        SELECT pi.member_id, pi.investment_amount 
        FROM project_investments pi 
        WHERE pi.project_id = project_id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Get total investment for this project
    SELECT COALESCE(SUM(investment_amount), 0) INTO total_investment
    FROM project_investments 
    WHERE project_id = project_id;
    
    IF total_investment > 0 THEN
        OPEN member_cursor;
        
        read_loop: LOOP
            FETCH member_cursor INTO member_id, investment_amount;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            -- Calculate member's share of profit
            SET member_profit = (investment_amount / total_investment) * total_profit;
            
            -- Insert profit transaction
            INSERT INTO transactions (member_id, amount, type, description, status, created_at)
            VALUES (member_id, member_profit, 'profit', CONCAT('Profit from project ID: ', project_id), 'completed', NOW());
            
            -- Update project investment record
            UPDATE project_investments 
            SET profit_amount = profit_amount + member_profit,
                profit_percentage = ((profit_amount + member_profit) / investment_amount) * 100
            WHERE project_id = project_id AND member_id = member_id;
            
        END LOOP;
        
        CLOSE member_cursor;
    END IF;
END //

DELIMITER ;

-- Create triggers
DELIMITER //

-- Trigger to update member balance after transaction
CREATE TRIGGER after_transaction_insert
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    -- Log the transaction
    INSERT INTO activity_logs (user_id, user_type, action, details, created_at)
    VALUES (NEW.member_id, 'member', 'transaction_created', 
            CONCAT('Transaction: ', NEW.type, ' - Amount: ', NEW.amount), NOW());
END //

-- Trigger to send notification after member approval
CREATE TRIGGER after_member_status_update
AFTER UPDATE ON members
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        IF NEW.status = 'approved' THEN
            INSERT INTO notifications (member_id, title, message, type, created_at)
            VALUES (NEW.id, 'সদস্যপদ অনুমোদিত', 
                    'অভিনন্দন! আপনার সদস্যপদ অনুমোদিত হয়েছে। এখন আপনি মাসিক জমা দিতে পারবেন।', 
                    'success', NOW());
        ELSEIF NEW.status = 'rejected' THEN
            INSERT INTO notifications (member_id, title, message, type, created_at)
            VALUES (NEW.id, 'সদস্যপদ প্রত্যাখ্যাত', 
                    'দুঃখিত! আপনার সদস্যপদের আবেদন প্রত্যাখ্যাত হয়েছে। আরো তথ্যের জন্য যোগাযোগ করুন।', 
                    'error', NOW());
        END IF;
    END IF;
END //

DELIMITER ;

-- Insert sample data for testing
INSERT INTO members (full_name, email, phone, address, ssc_roll, ssc_year, ssc_board, monthly_amount, status) VALUES
('মোহাম্মদ রহিম', '<EMAIL>', '01712345678', 'ঢাকা, বাংলাদেশ', '123456', '2020', 'dhaka', 2000.00, 'approved'),
('ফাতেমা খাতুন', '<EMAIL>', '01812345678', 'চট্টগ্রাম, বাংলাদেশ', '234567', '2019', 'chittagong', 1500.00, 'approved'),
('আহমেদ হাসান', '<EMAIL>', '01912345678', 'সিলেট, বাংলাদেশ', '345678', '2021', 'sylhet', 3000.00, 'pending')
ON DUPLICATE KEY UPDATE email = email;

-- Insert sample transactions
INSERT INTO transactions (member_id, amount, type, description, status, payment_method, created_at) VALUES
(1, 2000.00, 'deposit', 'মাসিক জমা - ডিসেম্বর ২০২৪', 'completed', 'bkash', '2024-12-01 10:00:00'),
(1, 2000.00, 'deposit', 'মাসিক জমা - নভেম্বর ২০২৪', 'completed', 'nagad', '2024-11-01 10:00:00'),
(2, 1500.00, 'deposit', 'মাসিক জমা - ডিসেম্বর ২০২৪', 'completed', 'bkash', '2024-12-01 11:00:00'),
(1, 500.00, 'profit', 'মাসিক লাভ বণ্টন', 'completed', NULL, '2024-11-25 15:00:00')
ON DUPLICATE KEY UPDATE id = id;
