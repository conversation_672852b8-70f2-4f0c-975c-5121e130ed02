<?php
require_once 'config/database.php';

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Validate and sanitize input
        $full_name = sanitize_input($_POST['full_name']);
        $email = sanitize_input($_POST['email']);
        $phone = sanitize_input($_POST['phone']);
        $address = sanitize_input($_POST['address']);
        $ssc_roll = sanitize_input($_POST['ssc_roll']);
        $ssc_year = sanitize_input($_POST['ssc_year']);
        $ssc_board = sanitize_input($_POST['ssc_board']);
        $monthly_amount = (float)$_POST['monthly_amount'];
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];

        // Validation
        if (empty($full_name) || empty($email) || empty($phone) || empty($address) || 
            empty($ssc_roll) || empty($ssc_year) || empty($ssc_board) || 
            empty($monthly_amount) || empty($password)) {
            throw new Exception('সকল ক্ষেত্র পূরণ করুন।');
        }

        if (!validate_email($email)) {
            throw new Exception('সঠিক ইমেইল ঠিকানা দিন।');
        }

        if (!validate_phone($phone)) {
            throw new Exception('সঠিক মোবাইল নম্বর দিন।');
        }

        if ($monthly_amount < 500) {
            throw new Exception('মাসিক জমার পরিমাণ কমপক্ষে ৫০০ টাকা হতে হবে।');
        }

        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            throw new Exception('পাসওয়ার্ড কমপক্ষে ' . PASSWORD_MIN_LENGTH . ' অক্ষরের হতে হবে।');
        }

        if ($password !== $confirm_password) {
            throw new Exception('পাসওয়ার্ড মিলছে না।');
        }

        // Check if email already exists
        $check_query = "SELECT id FROM members WHERE email = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$email]);
        
        if ($check_stmt->rowCount() > 0) {
            throw new Exception('এই ইমেইল ঠিকানা দিয়ে ইতিমধ্যে নিবন্ধন করা হয়েছে।');
        }

        // Handle file uploads
        $photo_url = '';
        $marksheet_url = '';

        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            try {
                $photo_url = upload_file($_FILES['photo'], 'uploads/photos/');
            } catch (Exception $e) {
                throw new Exception('ছবি আপলোডে সমস্যা: ' . $e->getMessage());
            }
        }

        if (isset($_FILES['marksheet']) && $_FILES['marksheet']['error'] == 0) {
            try {
                $marksheet_url = upload_file($_FILES['marksheet'], 'uploads/documents/');
            } catch (Exception $e) {
                throw new Exception('মার্কশিট আপলোডে সমস্যা: ' . $e->getMessage());
            }
        }

        // Hash password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        // Insert member data
        $insert_query = "INSERT INTO members (full_name, email, phone, address, ssc_roll, ssc_year, ssc_board, monthly_amount, photo_url, marksheet_url, password_hash, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
        
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->execute([
            $full_name, $email, $phone, $address, $ssc_roll, $ssc_year, 
            $ssc_board, $monthly_amount, $photo_url, $marksheet_url, $password_hash
        ]);

        $member_id = $db->lastInsertId();

        // Log activity
        log_activity($member_id, 'member_registered', 'New member registration: ' . $email);

        // Send notification email (if email function is configured)
        $email_subject = 'নিবন্ধন সফল - বন্ধুদের সংস্থা';
        $email_message = "
        <h2>স্বাগতম {$full_name}!</h2>
        <p>আপনার নিবন্ধন সফলভাবে সম্পন্ন হয়েছে। আপনার আবেদন পর্যালোচনা করা হবে এবং শীঘ্রই আপনাকে জানানো হবে।</p>
        <p><strong>আপনার তথ্য:</strong></p>
        <ul>
            <li>নাম: {$full_name}</li>
            <li>ইমেইল: {$email}</li>
            <li>মোবাইল: {$phone}</li>
            <li>মাসিক জমা: ৳{$monthly_amount}</li>
        </ul>
        <p>ধন্যবাদ!</p>
        ";
        
        send_email($email, $email_subject, $email_message);

        $success_message = 'নিবন্ধন সফল হয়েছে! আপনার আবেদন পর্যালোচনা করা হবে।';
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সদস্য নিবন্ধন - বন্ধুদের সংস্থা</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="flex items-center">
                        <i class="fas fa-users text-2xl mr-3"></i>
                        <h1 class="text-xl font-bold">বন্ধুদের সংস্থা</h1>
                    </a>
                </div>
                <div class="flex items-center space-x-6">
                    <a href="index.php" class="hover:text-blue-200">হোম</a>
                    <a href="dashboard.php" class="hover:text-blue-200">ড্যাশবোর্ড</a>
                    <a href="payment.php" class="hover:text-blue-200">পেমেন্ট</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 py-12">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">সদস্য নিবন্ধন</h1>
                <p class="text-gray-600">বন্ধুদের সংস্থায় যোগদানের জন্য নিচের ফর্মটি পূরণ করুন</p>
            </div>

            <?php if ($error_message): ?>
                <div class="alert-error mb-6">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert-success mb-6">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                <!-- Personal Information -->
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-user mr-1"></i>
                            পূর্ণ নাম *
                        </label>
                        <input type="text" name="full_name" class="form-control" placeholder="আপনার পূর্ণ নাম লিখুন" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-envelope mr-1"></i>
                            ইমেইল *
                        </label>
                        <input type="email" name="email" class="form-control" placeholder="<EMAIL>" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-phone mr-1"></i>
                            মোবাইল নম্বর *
                        </label>
                        <input type="tel" name="phone" class="form-control" placeholder="০১৭xxxxxxxx" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-money-bill mr-1"></i>
                            মাসিক জমার পরিমাণ (টাকা) *
                        </label>
                        <input type="number" name="monthly_amount" class="form-control" placeholder="১০০০" min="500" required>
                        <small class="text-gray-500">ন্যূনতম ৫০০ টাকা</small>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        ঠিকানা *
                    </label>
                    <textarea name="address" class="form-control" rows="3" placeholder="আপনার সম্পূর্ণ ঠিকানা লিখুন" required></textarea>
                </div>

                <!-- SSC Information -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        SSC পরীক্ষার তথ্য
                    </h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label class="form-label">রোল নম্বর *</label>
                            <input type="text" name="ssc_roll" class="form-control" placeholder="১২৩৪৫৬" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">পাশের সাল *</label>
                            <select name="ssc_year" class="form-control" required>
                                <option value="">সাল নির্বাচন করুন</option>
                                <?php for($year = date('Y'); $year >= 2000; $year--): ?>
                                    <option value="<?php echo $year; ?>"><?php echo $year; ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">শিক্ষা বোর্ড *</label>
                            <select name="ssc_board" class="form-control" required>
                                <option value="">বোর্ড নির্বাচন করুন</option>
                                <option value="dhaka">ঢাকা</option>
                                <option value="chittagong">চট্টগ্রাম</option>
                                <option value="rajshahi">রাজশাহী</option>
                                <option value="sylhet">সিলেট</option>
                                <option value="barisal">বরিশাল</option>
                                <option value="comilla">কুমিল্লা</option>
                                <option value="jessore">যশোর</option>
                                <option value="dinajpur">দিনাজপুর</option>
                                <option value="mymensingh">ময়মনসিংহ</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- File Uploads -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-upload mr-2"></i>
                        ফাইল আপলোড
                    </h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">আপনার ছবি (JPG/PNG, সর্বোচ্চ ২MB)</label>
                            <input type="file" name="photo" class="form-control" accept="image/*">
                            <small class="text-gray-500">পাসপোর্ট সাইজের ছবি আপলোড করুন</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">SSC মার্কশিট (PDF/JPG/PNG, সর্বোচ্চ ৫MB)</label>
                            <input type="file" name="marksheet" class="form-control" accept="image/*,.pdf">
                            <small class="text-gray-500">স্পষ্ট ও পড়ার যোগ্য ছবি আপলোড করুন</small>
                        </div>
                    </div>
                </div>

                <!-- Password -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-lock mr-2"></i>
                        পাসওয়ার্ড সেট করুন
                    </h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">পাসওয়ার্ড *</label>
                            <input type="password" name="password" class="form-control" placeholder="পাসওয়ার্ড লিখুন" required>
                            <small class="text-gray-500">কমপক্ষে ৬ অক্ষরের হতে হবে</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">পাসওয়ার্ড নিশ্চিত করুন *</label>
                            <input type="password" name="confirm_password" class="form-control" placeholder="পাসওয়ার্ড আবার লিখুন" required>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="border-t pt-6">
                    <div class="flex items-start">
                        <input type="checkbox" id="terms" class="mt-1 mr-3" required>
                        <label for="terms" class="text-sm text-gray-600">
                            আমি <a href="#" class="text-blue-600 hover:underline">নিয়মাবলী ও শর্তাবলী</a> পড়েছি এবং সম্মত আছি। 
                            আমি নিশ্চিত করছি যে প্রদত্ত সকল তথ্য সত্য ও সঠিক।
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="border-t pt-6">
                    <button type="submit" class="w-full btn-primary text-lg py-4">
                        <i class="fas fa-user-plus mr-2"></i>
                        নিবন্ধন সম্পন্ন করুন
                    </button>
                </div>
            </form>

            <div class="text-center mt-6">
                <p class="text-gray-600">
                    ইতিমধ্যে সদস্য আছেন? 
                    <a href="login.php" class="text-blue-600 hover:underline font-medium">লগইন করুন</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.querySelector('input[name="password"]').value;
            const confirmPassword = document.querySelector('input[name="confirm_password"]').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('পাসওয়ার্ড মিলছে না!');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে!');
                return false;
            }
        });

        // File size validation
        document.querySelector('input[name="photo"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.size > 2 * 1024 * 1024) {
                alert('ছবির সাইজ ২MB এর চেয়ে ছোট হতে হবে!');
                e.target.value = '';
            }
        });

        document.querySelector('input[name="marksheet"]').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.size > 5 * 1024 * 1024) {
                alert('মার্কশিটের সাইজ ৫MB এর চেয়ে ছোট হতে হবে!');
                e.target.value = '';
            }
        });
    </script>
</body>
</html>
