<?php
/**
 * SSLCommerz Payment Gateway Integration
 * Real payment processing for Friends Organization
 */

class SSLCommerz {
    private $store_id;
    private $store_password;
    private $is_live;
    private $gateway_url;
    private $validation_url;

    public function __construct() {
        // Load payment configuration
        require_once 'config/payment_config.php';

        $this->store_id = SSLCZ_STORE_ID;
        $this->store_password = SSLCZ_STORE_PASSWORD;
        $this->is_live = (PAYMENT_ENVIRONMENT === 'LIVE');
        $this->gateway_url = SSLCZ_REQUEST_URL;
        $this->validation_url = SSLCZ_VALIDATION_URL;
    }
    
    /**
     * Initiate payment session
     */
    public function initiatePayment($payment_data) {
        $post_data = array();
        
        // Store Information
        $post_data['store_id'] = $this->store_id;
        $post_data['store_passwd'] = $this->store_password;
        
        // Customer Information
        $post_data['total_amount'] = $payment_data['amount'];
        $post_data['currency'] = 'BDT';
        $post_data['tran_id'] = $payment_data['transaction_id'];
        $post_data['success_url'] = SSLCOMMERZ_SUCCESS_URL;
        $post_data['fail_url'] = SSLCOMMERZ_FAIL_URL;
        $post_data['cancel_url'] = SSLCOMMERZ_CANCEL_URL;
        $post_data['ipn_url'] = SSLCOMMERZ_IPN_URL;
        
        // Customer Information
        $post_data['cus_name'] = $payment_data['customer_name'];
        $post_data['cus_email'] = $payment_data['customer_email'];
        $post_data['cus_add1'] = $payment_data['customer_address'];
        $post_data['cus_city'] = 'Dhaka';
        $post_data['cus_state'] = 'Dhaka';
        $post_data['cus_postcode'] = '1000';
        $post_data['cus_country'] = 'Bangladesh';
        $post_data['cus_phone'] = $payment_data['customer_phone'];
        
        // Product Information
        $post_data['product_name'] = $payment_data['product_name'];
        $post_data['product_category'] = 'Service';
        $post_data['product_profile'] = 'general';
        
        // Shipping Information
        $post_data['shipping_method'] = 'NO';
        $post_data['num_of_item'] = 1;
        
        // EMI Information
        $post_data['emi_option'] = 0;
        
        // Additional Information
        $post_data['value_a'] = $payment_data['member_id'];
        $post_data['value_b'] = $payment_data['payment_type'];
        $post_data['value_c'] = date('Y-m-d H:i:s');
        
        return $this->makeRequest($post_data);
    }
    
    /**
     * Make cURL request to SSLCommerz
     */
    private function makeRequest($post_data) {
        $handle = curl_init();
        curl_setopt($handle, CURLOPT_URL, $this->gateway_url);
        curl_setopt($handle, CURLOPT_TIMEOUT, 30);
        curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($handle, CURLOPT_POST, 1);
        curl_setopt($handle, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $content = curl_exec($handle);
        $code = curl_getinfo($handle, CURLINFO_HTTP_CODE);
        
        if($code == 200 && !curl_errno($handle)) {
            curl_close($handle);
            $sslcommerzResponse = $content;
        } else {
            curl_close($handle);
            return false;
        }
        
        $sslcz = json_decode($sslcommerzResponse, true);
        return $sslcz;
    }
    
    /**
     * Validate payment response
     */
    public function validatePayment($val_id, $store_id, $store_passwd, $requested_url) {
        if($this->is_live) {
            $validation_url = "https://securepay.sslcommerz.com/validator/api/validationserverAPI.php";
        } else {
            $validation_url = "https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php";
        }
        
        $validation_url .= "?val_id=".$val_id."&store_id=".$store_id."&store_passwd=".$store_passwd."&v=1&format=json";
        
        $handle = curl_init();
        curl_setopt($handle, CURLOPT_URL, $validation_url);
        curl_setopt($handle, CURLOPT_TIMEOUT, 30);
        curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($handle, CURLOPT_POST, 0);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        
        $content = curl_exec($handle);
        $code = curl_getinfo($handle, CURLINFO_HTTP_CODE);
        
        if($code == 200 && !curl_errno($handle)) {
            curl_close($handle);
            $response = $content;
        } else {
            curl_close($handle);
            return false;
        }
        
        $result = json_decode($response, true);
        return $result;
    }
    
    /**
     * Generate unique transaction ID
     */
    public static function generateTransactionId() {
        return 'FO' . date('YmdHis') . rand(1000, 9999);
    }
    
    /**
     * Get payment methods for specific amount
     */
    public function getPaymentMethods($amount) {
        $methods = array();
        
        // Mobile Banking
        if ($amount >= 10) {
            $methods['mobile'] = array(
                'bkash' => array('name' => 'bKash', 'min' => 10, 'max' => 25000),
                'nagad' => array('name' => 'Nagad', 'min' => 10, 'max' => 25000),
                'rocket' => array('name' => 'Rocket', 'min' => 10, 'max' => 25000),
                'upay' => array('name' => 'Upay', 'min' => 10, 'max' => 25000)
            );
        }
        
        // Card Payment
        if ($amount >= 100) {
            $methods['card'] = array(
                'visa' => array('name' => 'Visa', 'min' => 100, 'max' => 500000),
                'mastercard' => array('name' => 'Mastercard', 'min' => 100, 'max' => 500000),
                'amex' => array('name' => 'American Express', 'min' => 100, 'max' => 500000)
            );
        }
        
        // Net Banking
        if ($amount >= 100) {
            $methods['netbanking'] = array(
                'dbbl' => array('name' => 'Dutch Bangla Bank', 'min' => 100, 'max' => 500000),
                'brac' => array('name' => 'BRAC Bank', 'min' => 100, 'max' => 500000),
                'city' => array('name' => 'City Bank', 'min' => 100, 'max' => 500000),
                'eastern' => array('name' => 'Eastern Bank', 'min' => 100, 'max' => 500000)
            );
        }
        
        return $methods;
    }
    
    /**
     * Format amount for display
     */
    public static function formatAmount($amount) {
        return number_format($amount, 2) . ' BDT';
    }
    
    /**
     * Log payment activity
     */
    public function logPayment($transaction_id, $status, $details = '') {
        global $db;
        
        try {
            $query = "INSERT INTO payment_logs (transaction_id, status, details, created_at) VALUES (?, ?, ?, NOW())";
            $stmt = $db->prepare($query);
            $stmt->execute([$transaction_id, $status, $details]);
        } catch (Exception $e) {
            error_log("Payment log error: " . $e->getMessage());
        }
    }
    
    /**
     * Send payment notification email
     */
    public function sendPaymentNotification($email, $transaction_id, $amount, $status) {
        $subject = "Payment " . ucfirst($status) . " - Friends Organization";
        
        if ($status == 'success') {
            $message = "
            <h2>Payment Successful!</h2>
            <p>Your payment has been processed successfully.</p>
            <p><strong>Transaction ID:</strong> {$transaction_id}</p>
            <p><strong>Amount:</strong> " . self::formatAmount($amount) . "</p>
            <p><strong>Date:</strong> " . date('d F Y, h:i A') . "</p>
            <p>Thank you for your payment!</p>
            ";
        } else {
            $message = "
            <h2>Payment Failed</h2>
            <p>Unfortunately, your payment could not be processed.</p>
            <p><strong>Transaction ID:</strong> {$transaction_id}</p>
            <p><strong>Amount:</strong> " . self::formatAmount($amount) . "</p>
            <p>Please try again or contact support.</p>
            ";
        }
        
        return send_email($email, $subject, $message);
    }
}

/**
 * Payment utility functions
 */
class PaymentUtils {
    
    /**
     * Validate payment amount
     */
    public static function validateAmount($amount, $min = 10, $max = 500000) {
        $amount = floatval($amount);
        return ($amount >= $min && $amount <= $max);
    }
    
    /**
     * Sanitize payment data
     */
    public static function sanitizePaymentData($data) {
        $sanitized = array();
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Generate payment receipt
     */
    public static function generateReceipt($transaction_data) {
        $receipt = "
        ==========================================
        বন্ধুদের সংস্থা - পেমেন্ট রসিদ
        ==========================================
        
        লেনদেন নম্বর: {$transaction_data['transaction_id']}
        তারিখ: " . date('d F Y, h:i A') . "
        
        সদস্যের নাম: {$transaction_data['customer_name']}
        ইমেইল: {$transaction_data['customer_email']}
        মোবাইল: {$transaction_data['customer_phone']}
        
        পেমেন্ট বিবরণ: {$transaction_data['product_name']}
        পরিমাণ: " . self::formatAmount($transaction_data['amount']) . "
        স্ট্যাটাস: সফল
        
        ধন্যবাদ!
        বন্ধুদের সংস্থা
        ==========================================
        ";
        
        return $receipt;
    }
    
    /**
     * Format amount for display
     */
    public static function formatAmount($amount) {
        return '৳' . number_format($amount, 2);
    }
}
?>
