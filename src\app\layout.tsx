import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'বন্ধুদের সংস্থা - Friends Organization',
  description: 'বন্ধুদের নিয়ে গঠিত সংস্থা যেখানে মাসিক সঞ্চয় ও লাভজনক বিনিয়োগ করা হয়',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="bn">
      <body className={inter.className}>
        <nav className="bg-primary-600 text-white shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-bold">বন্ধুদের সংস্থা</h1>
              </div>
              <div className="flex items-center space-x-4">
                <a href="/" className="hover:text-primary-200">হোম</a>
                <a href="/register" className="hover:text-primary-200">নিবন্ধন</a>
                <a href="/dashboard" className="hover:text-primary-200">ড্যাশবোর্ড</a>
                <a href="/payment" className="hover:text-primary-200">পেমেন্ট</a>
              </div>
            </div>
          </div>
        </nav>
        <main className="min-h-screen bg-gray-50">
          {children}
        </main>
        <footer className="bg-gray-800 text-white py-8">
          <div className="max-w-7xl mx-auto px-4 text-center">
            <p>&copy; ২০২৪ বন্ধুদের সংস্থা। সকল অধিকার সংরক্ষিত।</p>
          </div>
        </footer>
      </body>
    </html>
  )
}
