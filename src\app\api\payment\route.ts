import { NextRequest, NextResponse } from 'next/server'
import { PaymentService, PaymentData } from '@/lib/payment'
import { transactionService } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      amount,
      paymentMethod,
      provider,
      memberId,
      customerName,
      customerEmail,
      customerPhone
    } = body

    // Validate required fields
    if (!amount || !paymentMethod || !memberId || !customerName || !customerEmail) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate amount
    if (amount < 100 || amount > 100000) {
      return NextResponse.json(
        { error: 'Invalid payment amount' },
        { status: 400 }
      )
    }

    const paymentService = new PaymentService()
    const orderId = paymentService.generateOrderId()

    // Create payment data
    const paymentData: PaymentData = {
      amount,
      currency: 'BDT',
      description: `Monthly deposit - Friends Organization`,
      customerName,
      customerEmail,
      customerPhone: customerPhone || '',
      orderId
    }

    // Create transaction record in database
    const transaction = await transactionService.createTransaction({
      member_id: memberId,
      amount,
      type: 'deposit',
      description: 'Monthly deposit payment',
      status: 'pending',
      payment_method: `${paymentMethod}-${provider || 'default'}`,
      transaction_id: orderId
    })

    // Process payment
    const paymentResult = await paymentService.processPayment(
      paymentMethod,
      provider || 'default',
      paymentData
    )

    if (paymentResult.success) {
      return NextResponse.json({
        success: true,
        transactionId: transaction.id,
        orderId,
        paymentUrl: paymentResult.paymentUrl,
        message: 'Payment initiated successfully'
      })
    } else {
      // Update transaction status to failed
      await transactionService.updateTransactionStatus(transaction.id, 'failed')
      
      return NextResponse.json(
        { error: paymentResult.error || 'Payment initiation failed' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Payment API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const transactionId = searchParams.get('transactionId')

  if (!transactionId) {
    return NextResponse.json(
      { error: 'Transaction ID required' },
      { status: 400 }
    )
  }

  try {
    // Get transaction from database
    const transactions = await transactionService.getAllTransactions()
    const transaction = transactions.find(t => t.transaction_id === transactionId)

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      transaction: {
        id: transaction.id,
        amount: transaction.amount,
        status: transaction.status,
        createdAt: transaction.created_at,
        description: transaction.description
      }
    })
  } catch (error) {
    console.error('Transaction lookup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
