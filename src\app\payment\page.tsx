'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { CreditCard, Smartphone, Building, Shield, CheckCircle } from 'lucide-react'

interface PaymentForm {
  amount: number
  paymentMethod: 'card' | 'mobile' | 'bank'
  cardNumber?: string
  expiryDate?: string
  cvv?: string
  mobileNumber?: string
  mobileProvider?: string
  bankAccount?: string
  bankName?: string
}

export default function Payment() {
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentSuccess, setPaymentSuccess] = useState(false)
  const [selectedMethod, setSelectedMethod] = useState<'card' | 'mobile' | 'bank'>('mobile')

  const { register, handleSubmit, formState: { errors }, watch } = useForm<PaymentForm>({
    defaultValues: {
      amount: 2000,
      paymentMethod: 'mobile'
    }
  })

  const amount = watch('amount')

  const onSubmit = async (data: PaymentForm) => {
    setIsProcessing(true)
    try {
      // Here we would integrate with payment gateway (Stripe, SSLCommerz, etc.)
      console.log('Payment data:', data)
      
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      setPaymentSuccess(true)
    } catch (error) {
      alert('পেমেন্টে সমস্যা হয়েছে। আবার চেষ্টা করুন।')
    } finally {
      setIsProcessing(false)
    }
  }

  if (paymentSuccess) {
    return (
      <div className="max-w-2xl mx-auto px-4 py-12">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-green-600 mb-2">পেমেন্ট সফল!</h1>
          <p className="text-gray-600 mb-6">
            আপনার ৳{amount?.toLocaleString()} টাকার পেমেন্ট সফলভাবে সম্পন্ন হয়েছে।
          </p>
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-600">লেনদেন নম্বর</p>
            <p className="font-mono text-lg font-semibold">TXN{Date.now()}</p>
          </div>
          <div className="space-x-4">
            <button 
              onClick={() => setPaymentSuccess(false)}
              className="btn-primary"
            >
              আরেকটি পেমেন্ট করুন
            </button>
            <a href="/dashboard" className="btn-secondary">
              ড্যাশবোর্ডে ফিরে যান
            </a>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">পেমেন্ট করুন</h1>
          <p className="text-gray-600">নিরাপদ ও সহজ অনলাইন পেমেন্ট সিস্টেম</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Amount Selection */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4">পেমেন্টের পরিমাণ</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              {[1000, 2000, 3000, 5000].map((preset) => (
                <button
                  key={preset}
                  type="button"
                  onClick={() => {
                    const event = { target: { value: preset.toString() } } as any
                    register('amount').onChange(event)
                  }}
                  className={`p-3 border rounded-lg text-center font-medium transition-colors ${
                    amount === preset 
                      ? 'border-primary-600 bg-primary-50 text-primary-600' 
                      : 'border-gray-300 hover:border-primary-300'
                  }`}
                >
                  ৳{preset.toLocaleString()}
                </button>
              ))}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                কাস্টম পরিমাণ (টাকা)
              </label>
              <input
                type="number"
                {...register('amount', { 
                  required: 'পরিমাণ আবশ্যক',
                  min: { value: 100, message: 'ন্যূনতম ১০০ টাকা' }
                })}
                className="input-field"
                placeholder="পরিমাণ লিখুন"
              />
              {errors.amount && (
                <p className="text-red-500 text-sm mt-1">{errors.amount.message}</p>
              )}
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="card">
            <h2 className="text-xl font-semibold mb-4">পেমেন্ট পদ্ধতি নির্বাচন করুন</h2>
            <div className="grid md:grid-cols-3 gap-4 mb-6">
              <button
                type="button"
                onClick={() => setSelectedMethod('mobile')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  selectedMethod === 'mobile' 
                    ? 'border-primary-600 bg-primary-50' 
                    : 'border-gray-300 hover:border-primary-300'
                }`}
              >
                <Smartphone className="w-8 h-8 mx-auto mb-2 text-primary-600" />
                <p className="font-medium">মোবাইল ব্যাংকিং</p>
                <p className="text-sm text-gray-600">bKash, Nagad, Rocket</p>
              </button>

              <button
                type="button"
                onClick={() => setSelectedMethod('card')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  selectedMethod === 'card' 
                    ? 'border-primary-600 bg-primary-50' 
                    : 'border-gray-300 hover:border-primary-300'
                }`}
              >
                <CreditCard className="w-8 h-8 mx-auto mb-2 text-primary-600" />
                <p className="font-medium">ক্রেডিট/ডেবিট কার্ড</p>
                <p className="text-sm text-gray-600">Visa, Mastercard</p>
              </button>

              <button
                type="button"
                onClick={() => setSelectedMethod('bank')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  selectedMethod === 'bank' 
                    ? 'border-primary-600 bg-primary-50' 
                    : 'border-gray-300 hover:border-primary-300'
                }`}
              >
                <Building className="w-8 h-8 mx-auto mb-2 text-primary-600" />
                <p className="font-medium">ব্যাংক ট্রান্সফার</p>
                <p className="text-sm text-gray-600">সরাসরি ব্যাংক</p>
              </button>
            </div>

            {/* Mobile Banking Form */}
            {selectedMethod === 'mobile' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    মোবাইল ব্যাংকিং সেবা
                  </label>
                  <select
                    {...register('mobileProvider', { required: 'সেবা নির্বাচন করুন' })}
                    className="input-field"
                  >
                    <option value="">সেবা নির্বাচন করুন</option>
                    <option value="bkash">bKash</option>
                    <option value="nagad">Nagad</option>
                    <option value="rocket">Rocket</option>
                    <option value="upay">Upay</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    মোবাইল নম্বর
                  </label>
                  <input
                    type="tel"
                    {...register('mobileNumber', { required: 'মোবাইল নম্বর আবশ্যক' })}
                    className="input-field"
                    placeholder="০১৭xxxxxxxx"
                  />
                </div>
              </div>
            )}

            {/* Card Payment Form */}
            {selectedMethod === 'card' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    কার্ড নম্বর
                  </label>
                  <input
                    type="text"
                    {...register('cardNumber', { required: 'কার্ড নম্বর আবশ্যক' })}
                    className="input-field"
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      মেয়াদ উত্তীর্ণের তারিখ
                    </label>
                    <input
                      type="text"
                      {...register('expiryDate', { required: 'মেয়াদ আবশ্যক' })}
                      className="input-field"
                      placeholder="MM/YY"
                      maxLength={5}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CVV
                    </label>
                    <input
                      type="text"
                      {...register('cvv', { required: 'CVV আবশ্যক' })}
                      className="input-field"
                      placeholder="123"
                      maxLength={4}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Bank Transfer Form */}
            {selectedMethod === 'bank' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ব্যাংকের নাম
                  </label>
                  <select
                    {...register('bankName', { required: 'ব্যাংক নির্বাচন করুন' })}
                    className="input-field"
                  >
                    <option value="">ব্যাংক নির্বাচন করুন</option>
                    <option value="dutch-bangla">ডাচ-বাংলা ব্যাংক</option>
                    <option value="brac">ব্র্যাক ব্যাংক</option>
                    <option value="city">সিটি ব্যাংক</option>
                    <option value="eastern">ইস্টার্ন ব্যাংক</option>
                    <option value="islami">ইসলামী ব্যাংক</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    অ্যাকাউন্ট নম্বর
                  </label>
                  <input
                    type="text"
                    {...register('bankAccount', { required: 'অ্যাকাউন্ট নম্বর আবশ্যক' })}
                    className="input-field"
                    placeholder="আপনার অ্যাকাউন্ট নম্বর"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Security Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5 mr-3" />
              <div>
                <h3 className="font-medium text-blue-900">নিরাপত্তা নিশ্চয়তা</h3>
                <p className="text-sm text-blue-700 mt-1">
                  আপনার সকল পেমেন্ট তথ্য SSL এনক্রিপশন দিয়ে সুরক্ষিত। আমরা আপনার কার্ড বা ব্যাংক তথ্য সংরক্ষণ করি না।
                </p>
              </div>
            </div>
          </div>

          {/* Payment Summary */}
          <div className="card bg-gray-50">
            <h3 className="font-semibold mb-3">পেমেন্ট সারসংক্ষেপ</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>পরিমাণ:</span>
                <span className="font-medium">৳{amount?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>প্রসেসিং ফি:</span>
                <span className="font-medium">৳০</span>
              </div>
              <div className="border-t pt-2 flex justify-between text-lg font-bold">
                <span>মোট:</span>
                <span>৳{amount?.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isProcessing}
            className="w-full btn-primary text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? 'প্রসেসিং...' : `৳${amount?.toLocaleString()} পেমেন্ট করুন`}
          </button>
        </form>
      </div>
    </div>
  )
}
